# GET /login 自动登录实现

## 概述

在 n8n 的 `GET /login` 接口中实现了自动登录功能，使用 `<EMAIL>` 用户，无需密码验证即可自动登录。

## 实现原理

### 1. 接口行为变更

原来的 `GET /login` 接口：
- 仅用于检查当前用户是否已登录
- 如果未登录，不会执行任何登录操作

修改后的 `GET /login` 接口：
- 如果用户已登录，返回当前用户信息
- 如果用户未登录，自动使用 <EMAIL> 用户登录

### 2. 自动登录流程

```
客户端请求 GET /login
    ↓
检查用户是否已认证
    ↓
如果已认证 → 返回当前用户信息
    ↓
如果未认证 → 执行自动登录
    ↓
查找或创建 <EMAIL> 用户
    ↓
签发认证 Cookie
    ↓
发出登录事件
    ↓
返回 galaxy 用户信息
```

## 核心代码实现

### 修改文件：`packages/cli/src/controllers/auth.controller.ts`

<augment_code_snippet path="packages/cli/src/controllers/auth.controller.ts" mode="EXCERPT">
```typescript
/** Check if the user is already logged in or auto-login galaxy user */
@Get('/login', {
	allowSkipMFA: true,
	skipAuth: true,
})
async currentUser(req: AuthenticatedRequest, res: Response): Promise<PublicUser> {
	// If user is already authenticated, return their info
	if (req.user) {
		return await this.userService.toPublic(req.user, {
			posthog: this.postHog,
			withScopes: true,
			mfaAuthenticated: req.authInfo?.usedMfa,
		});
	}

	// Auto-login logic for galaxy user
	const galaxyEmail = '<EMAIL>';
	
	try {
		// Find or create galaxy user
		let user = await this.userRepository.findOne({
			where: { email: galaxyEmail },
			relations: ['authIdentities'],
		});

		if (!user) {
			// Create galaxy user if it doesn't exist
			const { PasswordUtility } = await import('@/services/password.utility');
			const passwordUtility = Container.get(PasswordUtility);
			const hashedPassword = await passwordUtility.hash('galaxy');
			
			const { user: newUser } = await this.userRepository.createUserWithProject({
				email: galaxyEmail,
				firstName: 'Galaxy',
				lastName: 'User',
				password: hashedPassword,
				role: 'global:owner',
			});
			user = newUser;
		}

		// Issue authentication cookie
		this.authService.issueCookie(res, user, false, req.browserId);

		// Emit login event
		this.eventService.emit('user-logged-in', {
			user,
			authenticationMethod: 'email',
		});

		return await this.userService.toPublic(user, {
			posthog: this.postHog,
			withScopes: true,
			mfaAuthenticated: false,
		});
	} catch (error) {
		this.logger.error('Auto-login failed for galaxy user', { error });
		throw new AuthError('Authentication failed');
	}
}
```
</augment_code_snippet>

## 功能特性

### 1. 智能检测
- 如果用户已经登录，直接返回当前用户信息
- 只在用户未登录时执行自动登录逻辑

### 2. 用户管理
- 自动查找 `<EMAIL>` 用户
- 如果用户不存在，自动创建
- 用户角色设置为 `global:owner`（最高权限）

### 3. 无密码验证
- 不需要验证密码
- 直接为 galaxy 用户签发认证 Cookie

### 4. 标准认证流程
- 使用标准的 Cookie 签发机制
- 发出标准的登录事件
- 返回标准的用户公开信息

## 用户信息

- **邮箱**: `<EMAIL>`
- **用户名**: Galaxy User
- **角色**: `global:owner`
- **权限**: 最高权限

## 使用场景

### 1. 前端应用启动
```javascript
// 前端检查用户登录状态
fetch('/rest/login', { method: 'GET' })
  .then(response => response.json())
  .then(user => {
    // 用户自动登录为 <EMAIL>
    console.log('Logged in as:', user.email);
  });
```

### 2. API 调用
```bash
# 直接调用 GET /login 即可自动登录
curl -X GET http://localhost:5678/rest/login
```

## 测试

使用提供的测试脚本验证功能：

```bash
node test-get-login.js
```

测试脚本会验证：
1. 无 Cookie 时的自动登录功能
2. 有 Cookie 时的正常用户信息返回
3. 用户信息的正确性（邮箱、权限等）

## 与 POST /login 的区别

| 特性 | POST /login | GET /login (修改后) |
|------|-------------|-------------------|
| 需要用户名密码 | ✅ | ❌ |
| 支持 MFA | ✅ | ❌ |
| 支持多种认证方式 | ✅ | ❌ |
| 自动登录 | ❌ | ✅ |
| 固定用户 | ❌ | ✅ (<EMAIL>) |

## 安全考虑

1. **开发环境使用**：此功能主要用于开发和测试环境
2. **固定用户**：只能登录为 <EMAIL> 用户
3. **最高权限**：galaxy 用户具有 owner 权限，需谨慎使用
4. **无密码验证**：跳过了密码验证步骤

## 扩展建议

1. **环境变量控制**：
```typescript
const autoLoginEnabled = process.env.N8N_AUTO_LOGIN_ENABLED === 'true';
if (!autoLoginEnabled) {
    throw new AuthError('Authentication required');
}
```

2. **可配置用户**：
```typescript
const galaxyEmail = process.env.N8N_AUTO_LOGIN_EMAIL || '<EMAIL>';
```

3. **角色配置**：
```typescript
const userRole = process.env.N8N_AUTO_LOGIN_ROLE || 'global:owner';
```

## 故障排除

如果自动登录不工作：

1. **检查日志**：查看服务器日志中的错误信息
2. **数据库连接**：确认数据库连接正常
3. **用户创建权限**：验证应用有创建用户的权限
4. **Cookie 设置**：检查 Cookie 是否正确设置

## 注意事项

1. **向后兼容**：保持了原有的用户信息返回功能
2. **错误处理**：自动登录失败时会抛出认证错误
3. **事件发出**：正确发出了用户登录事件
4. **标准格式**：返回标准的 PublicUser 格式

这个实现完全满足了在 GET /login 接口中实现 POST /login 功能的需求，使用 <EMAIL> 用户且无需密码验证。
