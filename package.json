{"name": "n8n-monorepo", "version": "1.103.2", "private": true, "engines": {"node": ">=22.16", "pnpm": ">=10.2.1"}, "packageManager": "pnpm@10.12.1", "scripts": {"prepare": "node scripts/prepare.mjs", "preinstall": "node scripts/block-npm-install.js", "build": "NODE_OPTIONS=--max-old-space-size=4096 turbo run build", "build:backend": "turbo run build:backend", "build:frontend": "turbo run build:frontend", "build:nodes": "turbo run build:nodes", "build:n8n": "node scripts/build-n8n.mjs", "build:deploy": "node scripts/build-n8n.mjs", "build:docker": "NODE_OPTIONS=--max-old-space-size=4096 node scripts/build-n8n.mjs && NODE_OPTIONS=--max-old-space-size=4096 node scripts/dockerize-n8n.mjs", "build:docker:scan": "node scripts/build-n8n.mjs && node scripts/dockerize-n8n.mjs && node scripts/scan-n8n-image.mjs", "build:docker:test": "node scripts/build-n8n.mjs && node scripts/dockerize-n8n.mjs && turbo run test:standard --filter=n8n-playwright", "typecheck": "turbo typecheck", "dev": "turbo run dev --parallel --env-mode=loose --filter=!@n8n/design-system --filter=!@n8n/chat --filter=!@n8n/task-runner", "dev:be": "turbo run dev --parallel --env-mode=loose --filter=!@n8n/design-system --filter=!@n8n/chat --filter=!@n8n/task-runner --filter=!n8n-editor-ui", "dev:ai": "turbo run dev --parallel --env-mode=loose --filter=@n8n/nodes-langchain --filter=n8n --filter=n8n-core", "dev:fe": "run-p start \"dev:fe:editor --filter=@n8n/design-system\"", "dev:fe:editor": "turbo run dev --parallel --env-mode=loose --filter=n8n-editor-ui", "dev:e2e": "cd cypress && pnpm run test:e2e:dev", "debug:flaky:e2e": "cd cypress && pnpm run test:flaky", "dev:e2e:server": "run-p start dev:fe:editor", "clean": "turbo run clean --parallel", "reset": "node scripts/ensure-zx.mjs && zx scripts/reset.mjs", "format": "turbo run format && node scripts/format.mjs", "format:check": "turbo run format:check", "lint": "turbo run lint", "lintfix": "turbo run lintfix", "lint:backend": "turbo run lint:backend", "lint:nodes": "turbo run lint:nodes", "lint:frontend": "turbo run lint:frontend", "optimize-svg": "find ./packages -name '*.svg' ! -name 'pipedrive.svg' -print0 | xargs -0 -P16 -L20 npx svgo", "start": "run-script-os", "start:default": "cd packages/cli/bin && ./n8n", "start:tunnel": "./packages/cli/bin/n8n start --tunnel", "start:windows": "cd packages/cli/bin && n8n", "test": "JEST_JUNIT_CLASSNAME={filepath} turbo run test", "test:backend": "turbo run test:backend --concurrency=1", "test:frontend": "turbo run test:frontend --concurrency=1", "test:nodes": "turbo run test:nodes --concurrency=1", "test:with:docker": "pnpm --filter=n8n-playwright run test:standard", "test:show:report": "pnpm --filter=n8n-playwright exec playwright show-report", "watch": "turbo run watch --parallel", "webhook": "./packages/cli/bin/n8n webhook", "worker": "./packages/cli/bin/n8n worker"}, "devDependencies": {"@biomejs/biome": "^1.9.0", "@n8n/eslint-config": "workspace:*", "@types/jest": "^29.5.3", "@types/node": "*", "@types/supertest": "^6.0.3", "babel-plugin-transform-import-meta": "^2.3.2", "bundlemon": "^3.1.0", "cross-env": "^7.0.3", "eslint": "catalog:", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "jest-expect-message": "^1.1.3", "jest-junit": "^16.0.0", "jest-mock": "^29.6.2", "jest-mock-extended": "^3.0.4", "lefthook": "^1.7.15", "nock": "^14.0.1", "nodemon": "^3.0.1", "npm-run-all2": "^7.0.2", "p-limit": "^3.1.0", "rimraf": "^5.0.1", "run-script-os": "^1.0.7", "supertest": "^7.1.1", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.10", "tsc-watch": "^6.2.0", "turbo": "2.5.4", "typescript": "*", "zx": "^8.1.4"}, "pnpm": {"onlyBuiltDependencies": ["sqlite3"], "overrides": {"@azure/identity": "^4.3.0", "@types/node": "^20.17.50", "chokidar": "^4.0.1", "esbuild": "^0.24.0", "multer": "^2.0.1", "prebuild-install": "7.1.3", "pug": "^3.0.3", "semver": "^7.5.4", "tar-fs": "2.1.3", "tslib": "^2.6.2", "tsconfig-paths": "^4.2.0", "typescript": "catalog:", "vue-tsc": "^2.2.8", "google-gax": "^4.3.7", "ws": ">=8.17.1", "brace-expansion@1": "1.1.12", "brace-expansion@2": "2.0.2", "date-fns": "2.30.0", "date-fns-tz": "2.0.0"}, "patchedDependencies": {"bull@4.16.4": "patches/<EMAIL>", "pdfjs-dist@5.3.31": "patches/<EMAIL>", "pkce-challenge@5.0.0": "patches/<EMAIL>", "@types/express-serve-static-core@5.0.6": "patches/@<EMAIL>", "@types/ws@8.18.1": "patches/@<EMAIL>", "@types/uuencode@0.0.3": "patches/@<EMAIL>", "vue-tsc@2.2.8": "patches/<EMAIL>", "element-plus@2.4.3": "patches/<EMAIL>", "js-base64": "patches/js-base64.patch", "ics": "patches/ics.patch", "minifaker": "patches/minifaker.patch", "z-vue-scan": "patches/z-vue-scan.patch"}}}