import { GlobalConfig } from '@n8n/config';
import { Container } from '@n8n/di';
import axios from 'axios';

const globalConfig = Container.get(GlobalConfig);

interface prjmResponse {
	code: number;
	message: string;
	result: galaxyProject;
}

interface galaxyProject {
	uid: string;
	name: string;
	email: string;
	projects: galaxyProjectRole[];
}

interface galaxyProjectRole {
	pname: string;
	display: string;
	roles: number[];
}

/**
 * 请求prjm服务, 获取uid所在项目的project_id
 * @uid 请求用户的uid
 */
export async function findGalaxyProjectIDByUid(uid: string): Promise<any> {
	const baseUrl = globalConfig.galaxy.prjmUrl; // 从配置中获取基础URL
	const uri = '/pm/v1/prj/uinfo?' + 'uid=' + uid;
	const url = baseUrl + uri;

	try {
		const response = await axios.get(url);
		return response.data.result;
	} catch (error) {
		return error;
	}
}
