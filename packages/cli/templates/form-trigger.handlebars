<!DOCTYPE html>
<html lang='en'>
	<head>
		<meta charset='UTF-8' />
		<meta name='viewport' content='width=device-width, initial-scale=1.0' />
		<meta name="description" content="{{formDescriptionMetadata}}" />
		<meta property="og:title" content="{{formTitle}}" />
		<meta property="og:description" content="{{formDescriptionMetadata}}" />
		<meta property="og:type" content="website" />
		<meta property="og:image" content="/static/og_image.png" />
		<link rel='icon' type='image/png' href='https://n8n.io/favicon.ico' />
		<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
		<title>{{formTitle}}</title>
		<style>
			:root {
				/* Fonts */
				--font-family: "Open Sans", sans-serif;
				--font-size-body: 12px;
				--font-size-label: 14px;
				--font-size-test-notice: 12px;
				--font-size-input: 14px;
				--font-size-header: 20px;
				--font-size-paragraph: 14px;
				--font-size-link: 12px;
				--font-size-error: 12px;
				--font-size-html-h1: 28px;
				--font-size-html-h2: 20px;
				--font-size-html-h3: 16px;
				--font-size-html-h4: 14px;
				--font-size-html-h5: 12px;
				--font-size-html-h6: 10px;

				/* Colors */
				--color-background: #fbfcfe;
				--color-test-notice-text: #e6a23d;
				--color-test-notice-bg: #fefaf6;
				--color-test-notice-border: #f6dcb7;
				--color-card-bg: #ffffff;
				--color-card-border: #dbdfe7;
				--color-card-shadow: rgba(99, 77, 255, 0.06);
				--color-link: #7e8186;
				--color-header: #525356;
				--color-label: #555555;
				--color-input-border: #dbdfe7;
				--color-input-text: #71747A;
				--color-focus-border: rgb(90, 76, 194);
				--color-submit-btn-bg: #ff6d5a;
				--color-submit-btn-text: #ffffff;
				--color-error: #ea1f30;
				--color-required: #ff6d5a;
				--color-clear-button-bg: #7e8186;
				--color-html-text: #555;
				--color-html-link: #FF6D5A;

				/* Border Radii */
				--border-radius-card: 8px;
				--border-radius-input: 6px;
				--border-radius-clear-btn: 50%;

				/* Spacing */
				--padding-container-top: 24px;
				--padding-card: 24px;
				--padding-test-notice-vertical: 12px;
				--padding-test-notice-horizontal: 24px;
				--margin-bottom-card: 16px;
				--padding-form-input: 12px;

				/* Dimensions */
				--container-width: 448px;
				--submit-btn-height: 48px;
				--checkbox-size: 18px;

				/* Others */
				--box-shadow-card: 0px 4px 16px 0px var(--color-card-shadow);
				--opacity-placeholder: 0.5;
			}

			*,
			::after,
			::before {
				box-sizing: border-box;
				margin: 0;
				padding: 0;
			}

			body {
				font-family: var(--font-family);
				font-weight: 400;
				font-size: var(--font-size-body);
				display: flex;
				flex-direction: column;
				justify-content: start;
				background-color: var(--color-background);
			}

			.container {
				margin: auto;
				text-align: center;
				padding-top: var(--padding-container-top);
				width: var(--container-width);
			}

			.test-notice {
				padding: var(--padding-test-notice-vertical) var(--padding-test-notice-horizontal);
				color: var(--color-test-notice-text);
				background-color: var(--color-test-notice-bg);
				border-radius: var(--border-radius-card);
				border: 1px solid var(--color-test-notice-border);
				font-size: var(--font-size-test-notice);
				font-weight: 400;
				line-height: 16px;
				text-align: left;
				margin-bottom: var(--margin-bottom-card);
			}

			.card {
				padding: var(--padding-card);
				background-color: var(--color-card-bg);
				border: 1px solid var(--color-card-border);
				border-radius: var(--border-radius-card);
				box-shadow: var(--box-shadow-card);
				margin-bottom: var(--margin-bottom-card);
			}

			.n8n-link {
				padding-bottom: var(--padding-container-top);
			}
			.n8n-link a {
				color: var(--color-link);
				font-weight: 600;
				font-size: var(--font-size-link);
				text-decoration: none;
			}

			.n8n-link svg {
				display: inline-block;
				vertical-align: middle;
			}

			.form-header h1 {
				color: var(--color-header);
				font-size: var(--font-size-header);
				font-weight: 400;
			}

			.form-header p {
				padding-top: 8px;
				color: var(--color-link);
				font-size: var(--font-size-paragraph);
				font-weight: 400;
			}

			.inputs-wrapper {
				padding-top: var(--padding-container-top);
				padding-bottom: var(--padding-container-top);
			}

			form label {
				display: block;
				text-align: left;
				font-size: var(--font-size-label);
				font-weight: 600;
				color: var(--color-label);
				padding-bottom: 6px;
			}

			form textarea {
				max-width: 100%;
			}

			form .form-input {
				border: 1px solid var(--color-input-border);
				border-radius: var(--border-radius-input);
				width: 100%;
				font-size: var(--font-size-input);
				color: var(--color-input-text);
				font-weight: 400;
				padding: var(--padding-form-input);
			}

			form textarea:focus,
			form input:focus {
				outline: none;
				border-color: var(--color-focus-border);
			}

			.select-input {
				border: 1px solid var(--color-input-border);
				border-radius: var(--border-radius-input);
			}

			.select-input:focus-within {
				border: 1px solid var(--color-focus-border);
			}

			form select {
				outline: transparent;
				border: none;
				border-radius: var(--border-radius-input);
				width: 100%;
				font-size: var(--font-size-input);
				color: var(--color-input-text);
				font-weight: 400;
				background-color: var(--color-card-bg);
				padding: var(--padding-form-input);
				border-right: 12px solid transparent;
			}

			input[type='date'] {
				font-family: var(--font-family);
			}

			::placeholder {
				opacity: var(--opacity-placeholder);
			}

			#submit-btn {
				width: 100%;
				height: var(--submit-btn-height);
				padding: var(--padding-form-input);
				border-radius: var(--border-radius-input);
				border: 0;
				font-size: var(--font-size-input);
				font-weight: 600;
				font-family: var(--font-family);
				background-color: var(--color-submit-btn-bg);
				color: var(--color-submit-btn-text);
				cursor: pointer;
			}

			#submit-btn span {
				padding-right: 6px;
				display: none;
			}

			#submit-btn span svg {
				display: inline-block;
				vertical-align: middle;
				fill: var(--color-submit-btn-text);
				animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
			}

			@keyframes lds-ring {
				0% {
					transform: rotate(0deg);
				}
				100% {
					transform: rotate(360deg);
				}
			}

			#submit-btn:hover {
				opacity: 0.7;
			}

			.error-hidden {
				display: block;
				position: relative;
				color: var(--color-error);
				text-align: left;
				font-size: var(--font-size-error);
				font-weight: 400;
				visibility: hidden;
				padding-top: 6px;
				padding-bottom: 9px;
			}

			.error-show {
				visibility: visible;
			}

			/* multiselect ----------------------------------- */
			.multiselect {
				padding-left: 6px;
				padding-right: 6px;
			}

			.multiselect-option {
				padding-top: 6px;
				display: flex;
			}

			.multiselect-option label {
				padding-left: 12px;
				color: var(--color-link);
				font-weight: 400;
				cursor: pointer;
			}

			.multiselect-checkbox {
				vertical-align: middle;
				min-width: var(--checkbox-size);
			}

			input[type='checkbox'] {
				width: var(--checkbox-size);
				height: var(--checkbox-size);
				cursor: pointer;
			}
			/* required field ----------------------------- */
			.form-required {
			}
			label.form-required::after {
				content: ' *';
				color: var(--color-required);
			}

			hr {
				border: 0;
				height: 1px;
				border-top: 1px solid var(--color-input-border);
				margin-top: 24px;
				margin-bottom: 24px;
				display: none;
			}

			.file-input-wrapper {
				position: relative;
				display: inline-block;
				width: 100%;
			}
			input[type="file"] {
			}
			.clear-button {
				position: absolute;
				right: 5px;
				top: 50%;
				transform: translateY(-65%);
				background-color: var(--color-clear-button-bg);
				border: none;
				border-radius: var(--border-radius-clear-btn);
				font-size: 14px;
				font-weight: 600;
				font-family: var(--font-family);
				color: white;
				width: 20px;
				height: 20px;
				text-align: center;
				line-height: 20px;
				cursor: pointer;
				display: none;
			}
			input[type="file"]:not(:empty) + .clear-button {
				display: inline-block;
			}

			div.html {
				text-align: left;
				color: var(--color-html-text);
				font-size: var(--font-size-input);
			}
			div.html h1, div.html h2, div.html h3, div.html h4, div.html h5, div.html h6, div.html p, div.html ul, div.html ol, div.html a {
				font-weight: 400;
				font-style: normal;
				margin-bottom: 8px;
			}
			div.html li {
				margin-bottom: 8px;
				margin-left: 24px;
			}
			div.html ul, div.html ol {
				font-size: var(--font-size-input);
				display: flex;
				flex-direction: column;
				align-self: stretch;
				line-height: normal;
			}
			div.html b {
				font-weight: 600;
			}
			div.html h1 {
				font-size: var(--font-size-html-h1);
				line-height: 35px;
			}
			div.html h2 {
				font-size: var(--font-size-html-h2);
				line-height: 26px;
			}
			div.html h3 {
				font-size: var(--font-size-html-h3);
				line-height: 24px;
			}
			div.html h4 {
				font-size: var(--font-size-html-h4);
				line-height: 18px;
			}
			div.html h5 {
				font-size: var(--font-size-html-h5);
			}
			div.html h6 {
				font-size: var(--font-size-html-h6);
			}
			div.html p {
				margin-bottom: 8px;
			}
			div.html a {
				color: var(--color-html-link);
				font-size: var(--font-size-input);
			}

			@media only screen and (max-width: 500px) {
				body {
					background-color: var(--color-background);
				}
				hr {
					display: block;
				}
				.container {
					width: 95%;
					min-height: 100vh;
					padding: 24px;
					border: 0px solid var(--color-input-border);
					border-radius: 0px;
					box-shadow: 0px 0px 0px 0px #ffffff;
				}
				.card {
					padding: 0px;
					background-color: var(--color-card-bg);
					border: 0px solid var(--color-input-border);
					border-radius: 0px;
					box-shadow: 0px 0px 0px 0px #ffffff;
					margin-bottom: 0px;
				}
			}
		</style>

		{{#if dangerousCustomCss}}
			<style>
				{{{ dangerousCustomCss }}}
			</style>
		{{/if}}
	</head>

	<body>
		<div class='container'>
			<section>
				{{#if testRun}}
					<div class='test-notice'>
						<p>This is a test version of your form</p>
					</div>
					<hr>
				{{/if}}

				<form class='card' action='#' method='POST' name='n8n-form' id='n8n-form' novalidate>
					<div class='form-header'>
						<h1>{{formTitle}}</h1>
						<p style="white-space: pre-line">{{{formDescription}}} </p>
					</div>

					<div class='inputs-wrapper'>
						{{#each formFields}}
							{{#if isMultiSelect}}
								<div>
									<label class='form-label {{inputRequired}}'>{{label}}</label>
									<div class='multiselect {{inputRequired}}' id='{{id}}'>
										{{#each multiSelectOptions}}
											<div class='multiselect-option'>
												<input type='checkbox' class='multiselect-checkbox' id='{{id}}' />
												<label for='{{id}}'>{{label}}</label>
											</div>
										{{/each}}
									</div>
									<p class='{{errorId}} error-hidden'>
										This field is required
									</p>
								</div>
							{{/if}}

							{{#if isSelect}}
								<div class='form-group'>
									<label class='form-label {{inputRequired}}' for='{{id}}'>{{label}}</label>
									<div class='select-input'>
										<select id='{{id}}' name='{{id}}' class='{{inputRequired}}'>
											<option value='' disabled selected>Select an option ...</option>
											{{#each selectOptions}}
												<option value='{{this}}'>{{this}}</option>
											{{/each}}
										</select>
									</div>
									<p class='{{errorId}} error-hidden'>
										This field is required
									</p>
								</div>
							{{/if}}

							{{#if isHtml}}
								<div class="form-group html">
									{{{html}}}
									<input type="hidden" id="{{id}}" name="{{id}}" value="{{html}}" />
								</div>
							{{/if}}

							{{#if isHidden}}
								<input type="hidden" id="{{id}}" name="{{id}}" value="{{hiddenValue}}" />
							{{/if}}

							{{#if isTextarea}}
								<div class='form-group'>
									<label class='form-label {{inputRequired}}' for='{{id}}'>{{label}}</label>
									<textarea
										class='form-input {{inputRequired}}'
										id='{{id}}'
										name='{{id}}'
										placeholder="{{placeholder}}"
									>{{defaultValue}}</textarea>
									<p class='{{errorId}} error-hidden'>
										This field is required
									</p>
								</div>
							{{/if}}

							{{#if isFileInput}}
								<div class='form-group file-input-wrapper'>
									<label class='form-label {{inputRequired}}' for='{{id}}'>{{label}}</label>
									<input
										class='form-input {{inputRequired}}'
										type='file'
										id='{{id}}'
										name='{{id}}'
										accept='{{acceptFileTypes}}'
										{{multipleFiles}}
										placeholder="{{placeholder}}"
									/>
									<button class="clear-button">&times;</button>
									<p class='{{errorId}} error-hidden'>
										This field is required
									</p>
								</div>
							{{/if}}

							{{#if isInput}}
								<div class='form-group'>
									<label class='form-label {{inputRequired}}' for='{{id}}'>{{label}}</label>
									<input
										class='form-input {{inputRequired}}'
										type='{{type}}'
										id='{{id}}'
										name='{{id}}'
										value="{{defaultValue}}"
										placeholder="{{placeholder}}"
									/>
									<p class='{{errorId}} error-hidden'>
										This field is required
									</p>
								</div>
							{{/if}}
						{{/each}}
					</div>

					<button id='submit-btn' type='submit'>
						<span><svg
								xmlns='http://www.w3.org/2000/svg'
								height='18px'
								viewBox='0 0 512 512'
							>
								<path
									d='M304 48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zm0 416a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm464-48a48 48 0 1 0 -96 0 48 48 0 1 0 96 0zM142.9 437A48 48 0 1 0 75 369.1 48 48 0 1 0 142.9 437zm0-294.2A48 48 0 1 0 75 75a48 48 0 1 0 67.9 67.9zM369.1 437A48 48 0 1 0 437 369.1 48 48 0 1 0 369.1 437z'
								/>
							</svg></span>
						{{ buttonLabel }}
					</button>
				</form>

				<div class='card' id='submitted-form' style='display: none;'>
					<div class='form-header'>
						{{#if formSubmittedHeader}}
							<h1 id='submitted-header'>{{formSubmittedHeader}}</h1>
						{{else}}
							<h1 id='submitted-header'>Form Submitted</h1>
						{{/if}}
						{{#if formSubmittedText}}
							<p id='submitted-content'>
								{{formSubmittedText}}
							</p>
						{{/if}}
					</div>
				</div>

				{{#if appendAttribution}}
					<hr>
					<div class='n8n-link'>
						<a href={{n8nWebsiteLink}} target='_blank'>
							Form automated with
							<svg
								width='73'
								height='20'
								viewBox='0 0 73 20'
								fill='none'
								xmlns='http://www.w3.org/2000/svg'
							>
								<path
									fill-rule='evenodd'
									clip-rule='evenodd'
									d='M40.2373 4C40.2373 6.20915 38.4464 8 36.2373 8C34.3735 8 32.8074 6.72525 32.3633 5H26.7787C25.801 5 24.9666 5.70685 24.8059 6.6712L24.6415 7.6576C24.4854 8.59415 24.0116 9.40925 23.3417 10C24.0116 10.5907 24.4854 11.4058 24.6415 12.3424L24.8059 13.3288C24.9666 14.2931 25.801 15 26.7787 15H28.3633C28.8074 13.2747 30.3735 12 32.2373 12C34.4464 12 36.2373 13.7908 36.2373 16C36.2373 18.2092 34.4464 20 32.2373 20C30.3735 20 28.8074 18.7253 28.3633 17H26.7787C24.8233 17 23.1546 15.5864 22.8331 13.6576L22.6687 12.6712C22.508 11.7069 21.6736 11 20.6959 11H19.0645C18.5652 12.64 17.0406 13.8334 15.2373 13.8334C13.434 13.8334 11.9094 12.64 11.4101 11H9.06449C8.56519 12.64 7.04059 13.8334 5.2373 13.8334C3.02817 13.8334 1.2373 12.0424 1.2373 9.83335C1.2373 7.6242 3.02817 5.83335 5.2373 5.83335C7.16069 5.83335 8.76699 7.19085 9.15039 9H11.3242C11.7076 7.19085 13.3139 5.83335 15.2373 5.83335C17.1607 5.83335 18.767 7.19085 19.1504 9H20.6959C21.6736 9 22.508 8.29315 22.6687 7.3288L22.8331 6.3424C23.1546 4.41365 24.8233 3 26.7787 3H32.3633C32.8074 1.27478 34.3735 0 36.2373 0C38.4464 0 40.2373 1.79086 40.2373 4ZM38.2373 4C38.2373 5.10455 37.3419 6 36.2373 6C35.1327 6 34.2373 5.10455 34.2373 4C34.2373 2.89543 35.1327 2 36.2373 2C37.3419 2 38.2373 2.89543 38.2373 4ZM5.2373 11.8334C6.34189 11.8334 7.23729 10.9379 7.23729 9.83335C7.23729 8.72875 6.34189 7.83335 5.2373 7.83335C4.13273 7.83335 3.2373 8.72875 3.2373 9.83335C3.2373 10.9379 4.13273 11.8334 5.2373 11.8334ZM15.2373 11.8334C16.3419 11.8334 17.2373 10.9379 17.2373 9.83335C17.2373 8.72875 16.3419 7.83335 15.2373 7.83335C14.1327 7.83335 13.2373 8.72875 13.2373 9.83335C13.2373 10.9379 14.1327 11.8334 15.2373 11.8334ZM32.2373 18C33.3419 18 34.2373 17.1045 34.2373 16C34.2373 14.8954 33.3419 14 32.2373 14C31.1327 14 30.2373 14.8954 30.2373 16C30.2373 17.1045 31.1327 18 32.2373 18Z'
									fill='#EA4B71'
								/>
								<path
									d='M44.2393 15.0007H46.3277V10.5791C46.3277 9.12704 47.2088 8.49074 48.204 8.49074C49.183 8.49074 49.9498 9.14334 49.9498 10.4812V15.0007H52.038V10.057C52.038 7.91969 50.798 6.67969 48.8567 6.67969C47.633 6.67969 46.9477 7.16914 46.4582 7.80544H46.3277L46.1482 6.84284H44.2393V15.0007Z'
									fill='#101330'
								/>
								<path
									d='M60.0318 9.50205V9.40415C60.7498 9.0452 61.4678 8.4252 61.4678 7.20155C61.4678 5.43945 60.0153 4.37891 58.0088 4.37891C55.9528 4.37891 54.4843 5.5047 54.4843 7.23415C54.4843 8.4089 55.1698 9.0452 55.9203 9.40415V9.50205C55.0883 9.79575 54.0928 10.6768 54.0928 12.1452C54.0928 13.9237 55.5613 15.1637 57.9923 15.1637C60.4233 15.1637 61.8428 13.9237 61.8428 12.1452C61.8428 10.6768 60.8638 9.81205 60.0318 9.50205ZM57.9923 5.87995C58.8083 5.87995 59.4118 6.40205 59.4118 7.2831C59.4118 8.16415 58.7918 8.6863 57.9923 8.6863C57.1928 8.6863 56.5238 8.16415 56.5238 7.2831C56.5238 6.38575 57.1603 5.87995 57.9923 5.87995ZM57.9923 13.5974C57.0458 13.5974 56.2793 12.9937 56.2793 11.9658C56.2793 11.0358 56.9153 10.3342 57.9758 10.3342C59.0203 10.3342 59.6568 11.0195 59.6568 11.9984C59.6568 12.9937 58.9223 13.5974 57.9923 13.5974Z'
									fill='#101330'
								/>
								<path
									d='M63.9639 15.0007H66.0524V10.5791C66.0524 9.12704 66.9334 8.49074 67.9289 8.49074C68.9079 8.49074 69.6744 9.14334 69.6744 10.4812V15.0007H71.7629V10.057C71.7629 7.91969 70.5229 6.67969 68.5814 6.67969C67.3579 6.67969 66.6724 7.16914 66.1829 7.80544H66.0524L65.8729 6.84284H63.9639V15.0007Z'
									fill='#101330'
								/>
							</svg>
						</a>
					</div>
				{{/if}}

				{{#if redirectUrl}}
					<a id='redirectUrl' href='{{redirectUrl}}' style='display: none;'></a>
				{{/if}}
				<input id="useResponseData" style="display: none;" value={{useResponseData}} />
			</section>
		</div>
		<script>
			function validateInput(input, errorElement) {
				const value = input.value.trim();
				const type = input.type;

				if (type === 'email' && value !== '') {
					return validateEmailInput(value, errorElement);
				} else if (type === 'number' && value !== '') {
					if (isNaN(value)) {
						errorElement.textContent = 'Enter only numbers in this field';
						errorElement.classList.add('error-show');
						return false;
					} else {
						errorElement.classList.remove('error-show');
						return true;
					}
				} else if (value === '') {
					errorElement.textContent = 'This field is required';
					errorElement.classList.add('error-show');
					return false;
				} else {
					errorElement.classList.remove('error-show');
					return true;
				}
			}

			function validateEmailInput(value, errorElement) {
				const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
				const isValidEmail = regex.test(value);

				if (!isValidEmail) {
					errorElement.textContent = 'Enter a valid email address in this field';
					errorElement.classList.add('error-show');
					return false;
				} else {
					errorElement.textContent = 'This field is required';
					errorElement.classList.remove('error-show');
					return true;
				}
			}

			function getSelectedValues(input) {
				const selectedValues = [];
				const checkboxes = input.querySelectorAll('.multiselect-checkbox');

				checkboxes.forEach((checkbox, index) => {
					if (checkbox.checked) {
						const label = input.querySelectorAll('label')[index];
						selectedValues.push(label.textContent.trim());
					}
				});

				return selectedValues;
			}

			function validateMultiselect(input, errorElement) {
				const selectedValues = getSelectedValues(input);

				if (!selectedValues.length) {
					errorElement.classList.add('error-show');
					return false;
				} else {
					errorElement.classList.remove('error-show');
					return true;
				}
			}

			const form = document.querySelector('#n8n-form');

			document.querySelectorAll("input[type=number]").forEach(function (element) {
				element.addEventListener("wheel", function(event) {
					if (document.activeElement === event.target) {
						event.preventDefault();
					}
				});
			});

			document.querySelectorAll('input[type="file"]').forEach(fileInput => {
				const clearButton = fileInput.nextElementSibling;
				let previousFiles = [];

				fileInput.addEventListener('change', () => {
					const files = fileInput.files;

					if (files.length > 0) {
						previousFiles = Array.from(files);
						clearButton.style.display = 'inline-block';
					} else {
						if (previousFiles.length > 0) {
							const dataTransfer = new DataTransfer();
							previousFiles.forEach(file => dataTransfer.items.add(file));
							fileInput.files = dataTransfer.files;
							clearButton.style.display = 'inline-block';
						}
					}
				});

				clearButton.addEventListener('click', (event) => {
					event.preventDefault();
					fileInput.value = '';
					previousFiles = [];
					clearButton.style.display = 'none';
				});
			});

			const requiredInputs = document.querySelectorAll('.form-required:not(label)');
			const emailInputs = document.querySelectorAll("input[type=email]");

			requiredInputs.forEach((input) => {
				const errorSelector = `.error-${input.id}`;
				const error = document.querySelector(errorSelector);

				if (input.classList.contains('multiselect')) {
					input.addEventListener('click', () => {
						validateMultiselect(input, error);
					});
				} else {
					input.addEventListener('blur', () => {
						validateInput(input, error);
					});
					input.addEventListener('input', () => {
						error.classList.remove('error-show');
					});
				}
			});

			emailInputs.forEach(function (input) {
				const errorSelector = `.error-${input.id}`;
				const error = document.querySelector(errorSelector);

				input.addEventListener("input", function(event) {
					const value = input.value.trim();
					if (value === "") {
						error.classList.remove('error-show');
					} else {
						validateEmailInput(value, error);
					}
				});
			});

			let interval = 1000;
			let timeoutId;
			let formWaitingUrl;

			const checkExecutionStatus = async () => {
				if (!interval) return;

				try {
					const response = await fetch(`${formWaitingUrl ?? window.location.href}/n8n-execution-status`);
					const text = (await response.text()).trim();

					if (text === "form-waiting") {
						window.location.replace(formWaitingUrl ?? window.location.href);
						return;
					}

					if (text === "success") {
						form.style.display = 'none';
						document.querySelector('#submitted-form').style.display = 'block';
						clearTimeout(timeoutId);
						return;
					}

					if (text === "null") {
						form.style.display = 'none';
						document.querySelector('#submitted-form').style.display = 'block';
						document.querySelector('#submitted-header').textContent = 'Could not get execution status';
						document.querySelector('#submitted-content').textContent =
							'Make sure "Save successful production executions" is enabled in your workflow settings';
						clearTimeout(timeoutId);
						return;
					}

					if(["canceled", "crashed", "error" ].includes(text)) {
						form.style.display = 'none';
						document.querySelector('#submitted-form').style.display = 'block';
						document.querySelector('#submitted-header').textContent = 'Problem submitting response';
						document.querySelector('#submitted-content').textContent =
							'Please try again or contact support if the problem persists';
						clearTimeout(timeoutId);
						return;
					}

					interval = Math.round(interval * 1.1);
					timeoutId = setTimeout(checkExecutionStatus, interval);
				} catch (error) {
					console.error("Error fetching data:", error);
				}
			};

			form.addEventListener('submit', (e) => {
				const valid = [];
				e.preventDefault();

				emailInputs.forEach(function (input) {
					const value = input.value.trim();
					if(value === '') {
						return;
					}
					const errorSelector = `.error-${input.id}`;
					const error = document.querySelector(errorSelector);
					valid.push(validateEmailInput(value, error));
				});

				requiredInputs.forEach((input) => {
					const errorSelector = `.error-${input.id}`;
					const error = document.querySelector(errorSelector);

					if (input.classList.contains('multiselect')) {
						valid.push(validateMultiselect(input, error));
					} else {
						valid.push(validateInput(input, error));
					}
				});

				if (valid.every((v) => v)) {
					var formData = new FormData();

					for (const filed of form.elements) {
						if(filed.type !== 'file') {
							formData.append(filed.name, filed.value);
						} else {
							for (const file of filed.files) {
								if(file.size === 0) {
									continue;
								}
								formData.append(filed.name, file);
							}
						}
					}
					document.querySelectorAll('.multiselect').forEach((multiselect) => {
 						const selectedValues = getSelectedValues(multiselect);
 						formData.append(multiselect.id, JSON.stringify(selectedValues));
 					});
					document.querySelector('#submit-btn').disabled = true;
					document.querySelector('#submit-btn').style.cursor = 'not-allowed';
					document.querySelector('#submit-btn span').style.display = 'inline-block';

					let postUrl = '';
					if (!window.location.href.includes('form-waiting')) {
						postUrl = window.location.search;
					}

					fetch(postUrl, {
						method: 'POST',
						body: formData,
					})
						.then(async function (response) {
							const useResponseData = document.getElementById("useResponseData").value;

							if (useResponseData === "true") {
								const text = await response.text();
								let json;

								try{
									json = JSON.parse(text);
								} catch (e) {}

								if(json?.formWaitingUrl) {
									formWaitingUrl = json.formWaitingUrl;
									clearTimeout(timeoutId);
									timeoutId = setTimeout(checkExecutionStatus, interval);
									return;
								}

								if (json?.redirectURL) {
									const url = json.redirectURL.includes("://") ? json.redirectURL : "https://" + json.redirectURL;
									window.location.replace(url);
									return;
								}

								if (json?.formSubmittedText) {
									form.style.display = 'none';
									document.querySelector('#submitted-form').style.display = 'block';
									document.querySelector('#submitted-content').textContent = json.formSubmittedText;
									return;
								}

								if (text) {
									document.body.innerHTML = text;
									return;
								}

								if (text === '') {
									// this is empty cleanup response from responsePromise
									// no need to keep checking execution status
									clearTimeout(timeoutId);
									interval = 0;
								}
							}

							if (response.status === 200) {
								if(response.redirected) {
									window.location.replace(response.url);
									return;
								}
								const redirectUrl = document.getElementById("redirectUrl");
								if (redirectUrl) {
									window.location.replace(redirectUrl.href);
								} else {
									form.style.display = 'none';
									document.querySelector('#submitted-form').style.display = 'block';
								}
							} else {
								form.style.display = 'none';
								document.querySelector('#submitted-form').style.display = 'block';
								document.querySelector('#submitted-header').textContent = 'Problem submitting response';
								document.querySelector('#submitted-content').textContent =
									'Please try again or contact support if the problem persists';
							}

							return;
						})
						.catch(function (error) {
							console.error('Error:', error);
						})
						.finally(() => {
							if (window.location.href.includes('form-waiting')) {
								clearTimeout(timeoutId);
								timeoutId = setTimeout(checkExecutionStatus, interval);
							}
						});
				}
			});
		</script>
	</body>
</html>
