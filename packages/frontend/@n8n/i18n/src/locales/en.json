{"_reusableBaseText": {"cancel": "Cancel", "codeNodeEditor": {"linter": {"useJson": "Access the properties of an item under `.json`, e.g. `item.json`"}, "completer": {"all": "Returns an array of the node's output items", "first": "Returns the first item output by the node", "last": "Returns the last item output by the node", "itemMatching": "Returns the matching item, i.e. the one used to produce the item in the current node at the specified index."}}, "name": "Name", "save": "Save", "dismiss": "<PERSON><PERSON><PERSON>", "unlimited": "Unlimited", "activate": "Activate", "user": "User", "enabled": "Enabled", "disabled": "Disabled"}, "_reusableDynamicText": {"readMore": "Read more", "learnMore": "Learn more", "moreInfo": "More info", "oauth2": {"clientId": "Client ID", "clientSecret": "Client Secret"}}, "generic.annotations": "Annotations", "generic.annotationData": "Highlighted data", "generic.any": "Any", "generic.cancel": "Cancel", "generic.open": "Open", "generic.close": "Close", "generic.confirm": "Confirm", "generic.create": "Create", "generic.create.workflow": "Create Workflow", "generic.deleteWorkflowError": "Problem deleting workflow", "generic.archiveWorkflowError": "Problem archiving workflow", "generic.unarchiveWorkflowError": "Problem unarchiving workflow", "generic.filtersApplied": "Filters are currently applied.", "generic.field": "field", "generic.fields": "fields", "generic.folderCount": "Folder | {count} Folder | {count} Folders", "generic.folder": "folder", "generic.learnMore": "Learn more", "generic.reset": "Reset", "generic.resetAllFilters": "Reset all filters", "generic.communityNode": "Community Node", "generic.communityNode.tooltip": "This is a node from our community. It's part of the {packageName} package. <a href=\"{docURL}\" target=\"_blank\" title=\"Read the n8n docs\">Learn more</a>", "generic.officialNode.tooltip": "This is an official node maintained by {author}", "generic.copy": "Copy", "generic.delete": "Delete", "generic.dontShowAgain": "Don't show again", "generic.enterprise": "Enterprise", "generic.executions": "Executions", "generic.tag_plural": "Tags", "generic.tag": "Tag | {count} Tags", "generic.tests": "Evaluations", "generic.optional": "optional", "generic.or": "or", "generic.clickToCopy": "Click to copy", "generic.copiedToClipboard": "Copied to clipboard", "generic.beta": "beta", "generic.yes": "Yes", "generic.no": "No", "generic.rating": "Rating", "generic.refresh": "Refresh", "generic.retry": "Retry", "generic.error": "Something went wrong", "generic.settings": "Settings", "generic.service": "the service", "generic.star": "Star", "generic.tryNow": "Try now", "generic.dismiss": "<PERSON><PERSON><PERSON>", "generic.unsavedWork.confirmMessage.headline": "Save changes before leaving?", "generic.unsavedWork.confirmMessage.message": "If you don't save, you will lose your changes.", "generic.unsavedWork.confirmMessage.confirmButtonText": "Save", "generic.unsavedWork.confirmMessage.cancelButtonText": "Leave without saving", "generic.upgrade": "Upgrade", "generic.upgradeNow": "Upgrade now", "generic.credential": "Credential | {count} Credential | {count} Credentials", "generic.credentials": "Credentials", "generic.workflow": "Workflow | {count} Workflow | {count} Workflows", "generic.workflowSaved": "Workflow changes saved", "generic.editor": "Editor", "generic.seePlans": "See plans", "generic.loading": "Loading", "generic.and": "and", "generic.ownedByMe": "(You)", "generic.moreInfo": "More info", "generic.next": "Next", "generic.pro": "Pro", "generic.variable_plural": "Variables", "generic.folders_plural": "Folders", "generic.variable": "Variable | {count} Variables", "generic.viewDocs": "View docs", "generic.workflows": "Workflows", "generic.rename": "<PERSON><PERSON>", "generic.missing.permissions": "Missing permissions to perform this action", "generic.shortcutHint": "Or press", "generic.upgradeToEnterprise": "Upgrade to Enterprise", "about.aboutN8n": "About n8n", "about.close": "Close", "about.license": "License", "about.n8nLicense": "Sustainable Use License + n8n Enterprise License", "about.n8nVersion": "n8n Version", "about.sourceCode": "Source Code", "about.instanceID": "Instance ID", "about.debug.title": "Debug", "about.debug.message": "Copy debug information", "about.debug.toast.title": "Debug info", "about.debug.toast.message": "Copied debug info to clipboard", "askAi.dialog.title": "'Ask AI' is almost ready", "askAi.dialog.body": "We’re still applying the finishing touches. Soon, you will be able to <strong>automatically generate code from simple text prompts</strong>. Join the waitlist to get early access to this feature.", "askAi.dialog.signup": "Join <PERSON>", "activationModal.butYouCanSeeThem": "but you can see them in the", "activationModal.executionList": "execution list", "activationModal.gotIt": "Got it", "activationModal.ifYouChooseTo": "if you choose to", "activationModal.saveExecutions": "save executions.", "activationModal.theseExecutionsWillNotShowUp": "These executions will not show up immediately in the editor,", "activationModal.workflowActivated": "Workflow activated", "activationModal.yourTriggerWillNowFire": "Your trigger will now fire production executions automatically.", "activationModal.yourTriggersWillNowFire": "Your triggers will now fire production executions automatically.", "activationModal.yourWorkflowWillNowListenForEvents": "Your workflow will now listen for events from {serviceName} and trigger executions.", "activationModal.yourWorkflowWillNowRegularlyCheck": "Your workflow will now regularly check {serviceName} for events and trigger executions for them.", "annotationTagsManager.manageTags": "Manage execution tags", "annotationTagsView.usage": "Usage (all workflows)", "annotationTagsView.inUse": "{count} execution | {count} executions", "auth.changePassword": "Change password", "auth.changePassword.currentPassword": "Current password", "auth.changePassword.mfaCode": "Two-factor code", "auth.changePassword.error": "Problem changing the password", "auth.changePassword.missingTokenError": "Missing token", "auth.changePassword.missingUserIdError": "Missing user ID", "auth.changePassword.passwordUpdated": "Password updated", "auth.changePassword.passwordUpdatedMessage": "You can now sign in with your new password", "auth.changePassword.passwordsMustMatchError": "Passwords must match", "auth.changePassword.reenterNewPassword": "Re-enter new password", "auth.changePassword.tokenValidationError": "Invalid password-reset token", "auth.defaultPasswordRequirements": "8+ characters, at least 1 number and 1 capital letter", "auth.validation.missingParameters": "Missing token or user id", "auth.email": "Email", "auth.firstName": "First Name", "auth.lastName": "Last Name", "auth.newPassword": "New password", "auth.password": "Password", "auth.role": "Role", "auth.roles.default": "<PERSON><PERSON><PERSON>", "auth.roles.member": "Member", "auth.roles.admin": "Admin", "auth.roles.owner": "Owner", "auth.agreement.label": "I want to receive security and product updates", "auth.setup.next": "Next", "auth.setup.settingUpOwnerError": "Problem setting up owner", "auth.setup.setupOwner": "Set up owner account", "auth.signin": "Sign in", "auth.signin.error": "Problem logging in", "auth.signout": "Sign out", "auth.signout.error": "Could not sign out", "auth.signup.finishAccountSetup": "Finish account setup", "auth.signup.missingTokenError": "Missing token", "auth.signup.setupYourAccount": "Set up your account", "auth.signup.setupYourAccountError": "Problem setting up your account", "auth.signup.tokenValidationError": "Issue validating invite token", "aiAssistant.name": "Assistant", "aiAssistant.n8nAi": "n8n AI", "aiAssistant.builder.name": "Builder", "aiAssistant.builder.mode": "AI Builder", "aiAssistant.builder.placeholder": "What would you like to automate?", "aiAssistant.builder.generateNew": "Generate new workflow", "aiAssistant.builder.buildWorkflow": "Build workflow", "aiAssistant.builder.newWorkflowNotice": "The created workflow will be added to the editor", "aiAssistant.builder.feedbackPrompt": "Is this workflow helpful?", "aiAssistant.builder.invalidPrompt": "Prompt validation failed. Please try again with a clearer description of your workflow requirements and supported integrations.", "aiAssistant.assistant": "AI Assistant", "aiAssistant.newSessionModal.title.part1": "Start new", "aiAssistant.newSessionModal.title.part2": "session", "aiAssistant.newSessionModal.message": "You already have an active AI Assistant session. Starting a new session will clear your current conversation history.", "aiAssistant.newSessionModal.question": "Are you sure you want to start a new session?", "aiAssistant.newSessionModal.confirm": "Start new session", "aiAssistant.serviceError.message": "Unable to connect to n8n's AI service ({message})", "aiAssistant.payloadTooBig.message": "Payload size is too large", "aiAssistant.codeUpdated.message.title": "Assistant modified workflow", "aiAssistant.codeUpdated.message.body1": "Open the", "aiAssistant.codeUpdated.message.body2": "node to see the changes", "aiAssistant.thinkingSteps.analyzingError": "Analyzing the error...", "aiAssistant.thinkingSteps.thinking": "Thinking...", "aiAssistant.prompts.currentView.workflowList": "The user is currently looking at the list of workflows.", "aiAssistant.prompts.currentView.credentialsList": "The user is currently looking at the list of credentials.", "aiAssistant.prompts.currentView.executionsView": "The user is currently looking at the list of executions for the currently open workflow.", "aiAssistant.prompts.currentView.workflowEditor": "The user is currently looking at the current workflow in n8n editor, without any specific node selected.", "aiAssistant.tooltip": "Ask Assistant", "banners.confirmEmail.message.1": "To secure your account and prevent future access issues, please confirm your", "banners.confirmEmail.message.2": "email address.", "banners.confirmEmail.button": "Confirm email", "banners.confirmEmail.toast.success.heading": "Confirmation email sent", "banners.confirmEmail.toast.success.message": "Please check your inbox and click the confirmation link.", "banners.confirmEmail.toast.error.heading": "Problem sending confirmation email", "banners.confirmEmail.toast.error.message": "Please try again later.", "banners.nonProductionLicense.message": "This n8n instance is not licensed for production purposes.", "banners.trial.message": "1 day left in your n8n trial | {count} days left in your n8n trial", "banners.trialOver.message": "Your trial is over. Upgrade now to keep automating.", "banners.v1.message": "n8n has been updated to version 1, introducing some breaking changes. Please consult the <a target=\"_blank\" href=\"https://docs.n8n.io/1-0-migration-checklist\">migration guide</a> for more information.", "binaryDataDisplay.backToList": "Back to list", "binaryDataDisplay.backToOverviewPage": "Back to overview page", "binaryDataDisplay.noDataFoundToDisplay": "No data found to display", "binaryDataDisplay.yourBrowserDoesNotSupport": "Your browser does not support the video element. Kindly update it to latest version.", "chat.hide": "Hide chat", "chat.open": "Open chat", "chat.window.title": "Cha<PERSON>", "chat.window.logs": "Latest Logs", "chat.window.logsFromNode": "from {nodeName} node", "chat.window.noChatNode": "No Chat Node", "chat.window.noExecution": "Nothing got executed yet", "chat.window.chat.placeholder": "Type message, or press ‘up’ for prev one", "chat.window.chat.placeholderPristine": "Type a message", "chat.window.chat.sendButtonText": "Send", "chat.window.chat.provideMessage": "Please provide a message", "chat.window.chat.emptyChatMessage": "Empty chat message", "chat.window.chat.emptyChatMessage.v2": "Send a message below to trigger the chat workflow", "chat.window.chat.chatMessageOptions.reuseMessage": "Reuse Message", "chat.window.chat.chatMessageOptions.repostMessage": "Repost Message", "chat.window.chat.chatMessageOptions.executionId": "Execution ID", "chat.window.chat.unpinAndExecute.description": "Sending the message overwrites the pinned chat node data.", "chat.window.chat.unpinAndExecute.title": "Unpin chat output data?", "chat.window.chat.unpinAndExecute.confirm": "Unpin and send", "chat.window.chat.unpinAndExecute.cancel": "Cancel", "chat.window.chat.response.empty": "[No response. Make sure the last executed node outputs the content to display here]", "chat.window.session.title": "Session", "chat.window.session.id": "Session: {id}", "chat.window.session.id.copy": "(click to copy)", "chat.window.session.reset": "Reset", "chat.window.session.resetSession": "Reset chat session", "chatEmbed.infoTip.description": "Add chat to external applications using the n8n chat package.", "chatEmbed.infoTip.link": "More info", "chatEmbed.title": "Embed Chat in your website", "chatEmbed.close": "Close", "chatEmbed.install": "First, install the n8n chat package:", "chatEmbed.paste.cdn": "Paste the following code anywhere in the {code} tag of your HTML file.", "chatEmbed.paste.cdn.file": "<body>", "chatEmbed.paste.vue": "Next, paste the following code in your {code} file.", "chatEmbed.paste.vue.file": "App.vue", "chatEmbed.paste.react": "Next, paste the following code in your {code} file.", "chatEmbed.paste.react.file": "App.ts", "chatEmbed.paste.other": "Next, paste the following code in your {code} file.", "chatEmbed.paste.other.file": "main.ts", "chatEmbed.packageInfo.description": "The n8n Chat widget can be easily customized to fit your needs.", "chatEmbed.packageInfo.link": "Read the full documentation", "chatEmbed.chatTriggerNode": "You can use a Chat Trigger Node to embed the chat widget directly into n8n.", "chatEmbed.url": "https://www.npmjs.com/package/{'@'}n8n/chat", "codeEdit.edit": "Edit", "codeNodeEditor.askAi": "✨ Ask AI", "codeNodeEditor.completer.$()": "Output data of the {nodeName} node", "codeNodeEditor.completer.$execution": "Retrieve or set metadata for the current execution", "codeNodeEditor.completer.$execution.id": "The ID of the current workflow execution", "codeNodeEditor.completer.$execution.mode": "Returns either <code>test</code> (meaning the execution was triggered by clicking a button in n8n) or <code>production</code> (meaning the execution was triggered automatically)", "codeNodeEditor.completer.$execution.resumeUrl": "The webhook URL to call to resume a workflow waiting at a 'Wait' node.", "codeNodeEditor.completer.$execution.resumeFormUrl": "The URL to access a form generated by the 'Wait' node.", "codeNodeEditor.completer.$execution.customData": "Set and get custom execution data (e.g. to filter executions by). You can also do this with the 'Execution Data' node.", "codeNodeEditor.completer.$execution.customData.set": "Stores custom execution data under the key specified. Use this to easily filter executions by this data.", "codeNodeEditor.completer.$execution.customData.set.args.key": "The key (identifier) under which the data is stored", "codeNodeEditor.completer.$execution.customData.set.args.value": "The data to store", "codeNodeEditor.completer.$execution.customData.set.examples.1": "Store the user's email, to easily retrieve all execs related to that user later", "codeNodeEditor.completer.$execution.customData.get": "Returns the custom execution data stored under the given key.", "codeNodeEditor.completer.$execution.customData.get.args.key": "The key (identifier) under which the data is stored", "codeNodeEditor.completer.$execution.customData.get.examples.1": "Get the user's email (which was previously stored)", "codeNodeEditor.completer.$execution.customData.setAll": "Sets multiple key-value pairs of  custom data for the execution. Use this to easily filter executions by this data.", "codeNodeEditor.completer.$execution.customData.setAll.args.obj": "A JavaScript object containing key-value pairs of the data to set", "codeNodeEditor.completer.$execution.customData.getAll": "Returns all the key-value pairs of custom data that have been set in the current execution.", "codeNodeEditor.completer.$ifEmpty": "Returns the first parameter if it isn't empty, otherwise returns the second parameter. The following count as empty: <code>\"</code>, <code>[]</code>, <code>{'{}'}</code>, <code>null</code>, <code>undefined</code>", "codeNodeEditor.completer.$ifEmpty.args.value": "The value to return, provided it isn't empty", "codeNodeEditor.completer.$ifEmpty.args.valueIfEmpty": "What to return if <code>value</code> is empty", "codeNodeEditor.completer.$input": "The input data of the current node", "codeNodeEditor.completer.$input.all": "@:_reusableBaseText.codeNodeEditor.completer.all", "codeNodeEditor.completer.$input.first": "@:_reusableBaseText.codeNodeEditor.completer.first", "codeNodeEditor.completer.$input.item": "The item that generated the current one", "codeNodeEditor.completer.$input.itemMatching": "@:_reusableBaseText.codeNodeEditor.completer.itemMatching", "codeNodeEditor.completer.$input.last": "@:_reusableBaseText.codeNodeEditor.completer.last", "codeNodeEditor.completer.$itemIndex": "The position of the item currently being processed in the list of input items", "codeNodeEditor.completer.$jmespath": "Extracts data from an object (or array of objects) using a <a target=\"_blank\" href=\"https://docs.n8n.io/code/cookbook/jmespath/\">JMESPath</a> expression. Useful for querying complex, nested objects. Returns <code>undefined</code> if the expression is invalid.", "codeNodeEditor.completer.$jmespath.args.obj": "The Object or array of Objects to retrieve data from", "codeNodeEditor.completer.$jmespath.args.expression": "A <a target=\"_blank\" href=\"https://jmespath.org/examples.html\">JMESPath expression</a> defining the data to retrieve from the object", "codeNodeEditor.completer.$jmespath.examples.1": "Get all names, in an array", "codeNodeEditor.completer.$jmespath.examples.2": "Get the names and ages of everyone under 20", "codeNodeEditor.completer.$jmespath.examples.3": "Get the name of the first person under 20", "codeNodeEditor.completer.$jmespath.examples.4": "Get the names of all the guests in each reservation that require a double room", "codeNodeEditor.completer.$if": "Returns one of two values depending on the <code>condition</code>. Similar to the <code>?</code> operator in JavaScript.", "codeNodeEditor.completer.$if.args.condition": "The check to make. Should evaluate to either <code>true</code> or <code>false</code>", "codeNodeEditor.completer.$if.args.valueIfTrue": "The value to return if the condition is true", "codeNodeEditor.completer.$if.args.valueIfFalse": "The value to return if the condition is false", "codeNodeEditor.completer.$if.examples.1": "Return \"Good day\" if time is before 5pm, otherwise \"Good evening\"", "codeNodeEditor.completer.$if.examples.2": "$if() calls can be combined\nReturn \"Good morning\" if time is before 10am, \"Good day\" it's before 5pm, otherwise \"Good evening\"", "codeNodeEditor.completer.$max": "Returns the highest of the given numbers, or -Infinity if there are no parameters.", "codeNodeEditor.completer.$max.args.numbers": "The numbers to compare", "codeNodeEditor.completer.$min": "Returns the lowest of the given numbers, or Infinity if there are no parameters.", "codeNodeEditor.completer.$now": "A DateTime representing the current moment. \n\nUses the workflow's time zone (which can be changed in the workflow settings).", "codeNodeEditor.completer.$parameter": "The configuration settings of the current node. These are the parameters you fill out within the node's UI (e.g. its operation).", "codeNodeEditor.completer.$prevNode": "Information about the node that the current input came from. \n\nWhen in a 'Merge' node, always uses the first input connector.", "codeNodeEditor.completer.$prevNode.name": "The name of the node that the current input came from. \n\nAlways uses the current node's first input connector if there is more than one (e.g. in the 'Merge' node).", "codeNodeEditor.completer.$prevNode.outputIndex": "The index of the output connector that the current input came from. Use this when the previous node had multiple outputs (such as an 'If' or 'Switch' node). \n\nAlways uses the current node's first input connector if there is more than one (e.g. in the 'Merge' node).", "codeNodeEditor.completer.$prevNode.runIndex": "The run of the previous node that generated the current input. \n\nAlways uses the current node's first input connector if there is more than one (e.g. in the 'Merge' node). ", "codeNodeEditor.completer.$runIndex": "The index of the current run of the current node execution. Starts at 0.", "codeNodeEditor.completer.$nodeVersion": "The version of the current node (as displayed at the bottom of the nodes's settings pane)", "codeNodeEditor.completer.$today": "A DateTime representing midnight at the start of the current day. \n\nUses the instance's time zone (unless overridden in the workflow's settings).", "codeNodeEditor.completer.$vars": "The <a target=\"_blank\"  href=\"https://docs.n8n.io/code/variables/\">variables</a> available to the workflow", "codeNodeEditor.completer.$vars.varName": "Variable set on this n8n instance. All variables evaluate to strings.", "codeNodeEditor.completer.$secrets": "The secrets from an <a target=\"_blank\" href=\"https://docs.n8n.io/external-secrets/\">external secrets vault</a>, if configured. Secret values are never displayed to the user. Only available in credential fields.", "codeNodeEditor.completer.$secrets.provider": "External secrets providers connected to this n8n instance.", "codeNodeEditor.completer.$secrets.provider.varName": "External secrets connected to this n8n instance. All secrets evaluate to strings.", "codeNodeEditor.completer.$workflow": "Information about the current workflow", "codeNodeEditor.completer.$workflow.active": "Whether the workflow is active", "codeNodeEditor.completer.$workflow.id": "The workflow ID. Can also be found in the workflow's URL.", "codeNodeEditor.completer.$workflow.name": "The name of the workflow, as shown at the top of the editor", "codeNodeEditor.completer.$response": "The response returned by the last HTTP call. Only available in the 'HTTP Request' node.", "codeNodeEditor.completer.$response.headers": "The headers returned by the last HTTP call. Only available in the 'HTTP Request' node.", "codeNodeEditor.completer.$response.statusCode": "The HTTP status code returned by the last HTTP call. Only available in the 'HTTP Request' node.", "codeNodeEditor.completer.$response.statusMessage": "An optional message regarding the request status. Only available in the 'HTTP Request' node.", "codeNodeEditor.completer.$response.body": "The body of the response object from the last HTTP call. Only available in the 'HTTP Request' node", "codeNodeEditor.completer.$request": "The request object sent during the last run of the node. Only available in the 'HTTP Request' node.", "codeNodeEditor.completer.$pageCount": "The number of results pages the node has fetched. Only available in the 'HTTP Request' node.", "codeNodeEditor.completer.dateTime": "Luxon DateTime. Use this object to parse, format and manipulate dates and times.", "codeNodeEditor.completer.binary": "Returns any binary input data to the current node, for the current item. Shorthand for <code>$input.item.binary</code>.", "codeNodeEditor.completer.binary.mimeType": "A string representing the format of the file's contents, e.g. <code>image/jpeg</code>", "codeNodeEditor.completer.binary.fileSize": "A string representing the size of the file (e.g. <code>1 kB</code>)", "codeNodeEditor.completer.binary.fileName": "The name of the file, including extension", "codeNodeEditor.completer.binary.fileExtension": "The suffix attached to the filename (e.g. <code>txt</code>)", "codeNodeEditor.completer.binary.fileType": "A string representing the type of the file, e.g. <code>image</code>. Corresponds to the first part of the MIME type.", "codeNodeEditor.completer.binary.id": "The unique ID of the file. Used to identify the file when it is stored on disk or in a storage service such as S3.", "codeNodeEditor.completer.binary.directory": "The path to the directory that the file is stored in. Useful for distinguishing between files with the same name in different directories. Not set if n8n is  configured to store files in its database.", "codeNodeEditor.completer.item.binary": "Returns any binary data the item contains.", "codeNodeEditor.completer.item.json": "Returns the JSON data the item contains.", "codeNodeEditor.completer.math": "Mathematical utility methods", "codeNodeEditor.completer.globalObject": "Methods to manipulate JavaScript Objects", "codeNodeEditor.completer.globalObject.assign": "Merge all enumerable object properties into a target object. Returns the modified target object.", "codeNodeEditor.completer.globalObject.entries": "The object's keys and values", "codeNodeEditor.completer.globalObject.keys": "The object's keys", "codeNodeEditor.completer.globalObject.values": "The object's values", "codeNodeEditor.completer.json": "Returns the JSON input data to the current node, for the current item. Shorthand for <code>$input.item.json</code>.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.expandFormat": "Produce the the fully expanded format token for the locale Does NOT quote characters, so quoted tokens will not round trip correctly.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromFormat": "Create a DateTime from an input string and format string.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromFormatExplain": "Explain how a string would be parsed by fromFormat().", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromHTTP": "Create a DateTime from an HTTP header date", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromISO": "Create a DateTime from an ISO 8601 string", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromISO.args.isoString": "ISO 8601 string to convert to a DateTime", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromISO.args.opts": "Configuration options. See  See <a target=\"blank\" href=\"https://moment.github.io/luxon/api-docs/index.html#datetimefromiso\">Luxon docs</a> for more info.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromJSDate": "Create a DateTime from a JavaScript Date object. Uses the default zone", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromMillis": "Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromMillis.args.milliseconds": "Number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC)", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromMillis.args.opts": "Configuration options. See  See <a target=\"blank\" href=\"https://moment.github.io/luxon/api-docs/index.html#datetimefrommillis\">Luxon docs</a> for more info.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromObject": "Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromRFC2822": "Create a DateTime from an RFC 2822 string", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromString": "Deprecated: use `fromFormat` instead.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromStringExplain": "Deprecated: use `fromFormatExplain` instead.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSQL": "Create a DateTime from a SQL date, time, or datetime", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSeconds": "Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSeconds.args.seconds": "Number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC)", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.fromSeconds.args.opts": "Configuration options. See <a target=\"blank\" href=\"https://moment.github.io/luxon/api-docs/index.html#datetimefromseconds\">Luxon docs</a> for more info.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.invalid": "Create an invalid DateTime.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.isDateTime": "Check if an object is a DateTime. Works across context boundaries", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.isDateTime.args.maybeDateTime": "Potential DateTime to check. Only instances of the Luxon DateTime class will return <code>true</code>.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.local": "Create a local DateTime", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.max": "Return the max of several date times.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.max.args.dateTimes": "DateTime objects to compare", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.min": "Return the min of several date times.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.min.args.dateTimes": "DateTime objects to compare", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.now": "Create a DateTime for the current instant, in the workflow's local time zone", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.parseFormatForOpts": "Produce the the fully expanded format token for the locale Does NOT quote characters, so quoted tokens will not round trip correctly.", "codeNodeEditor.completer.luxon.dateTimeStaticMethods.utc": "Create a DateTime in UTC", "codeNodeEditor.completer.luxon.instanceMethods.day": "The day of the month (1-31).", "codeNodeEditor.completer.luxon.instanceMethods.daysInMonth": "Returns the number of days in this DateTime's month.", "codeNodeEditor.completer.luxon.instanceMethods.daysInYear": "Returns the number of days in this DateTime's year.", "codeNodeEditor.completer.luxon.instanceMethods.diff": "Return the difference between two DateTimes as a Duration.", "codeNodeEditor.completer.luxon.instanceMethods.diffNow": "Return the difference between this DateTime and right now.", "codeNodeEditor.completer.luxon.instanceMethods.endOf": "Rounds the DateTime up to the end of one of its units, e.g. the end of the month", "codeNodeEditor.completer.luxon.instanceMethods.endOf.args.unit": "The unit to round to the end of. Can be <code>year</code>, <code>quarter</code>, <code>month</code>, <code>week</code>, <code>day</code>, <code>hour</code>, <code>minute</code>, <code>second</code>, or <code>millisecond</code>.", "codeNodeEditor.completer.luxon.instanceMethods.endOf.args.opts": "Object with options that affect the output. Possible properties:\n<code>useLocaleWeeks</code> (boolean): Whether to use the locale when calculating the start of the week. Defaults to false.", "codeNodeEditor.completer.luxon.instanceMethods.equals": "Returns <code>true</code> if the two DateTimes represent exactly the same moment and are in the same time zone. For a less strict comparison, use <code>hasSame()</code>.", "codeNodeEditor.completer.luxon.instanceMethods.equals.args.other": "The other DateTime to compare", "codeNodeEditor.completer.luxon.instanceMethods.hasSame": "Returns <code>true</code> if the two DateTimes are the same, down to the unit specified. Time zones are ignored (only local times are compared), so use <code>toUTC()</code> first if needed.", "codeNodeEditor.completer.luxon.instanceMethods.hasSame.args.other": "The other DateTime to compare", "codeNodeEditor.completer.luxon.instanceMethods.hasSame.args.unit": "The unit of time to check sameness down to. One of <code>year</code>, <code>quarter</code>, <code>month</code>, <code>week</code>, <code>day</code>, <code>hour</code>, <code>minute</code>, <code>second</code>, or <code>millisecond</code>.", "codeNodeEditor.completer.luxon.instanceMethods.hour": "The hour of the day (0-23).", "codeNodeEditor.completer.luxon.instanceMethods.invalidExplanation": "Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid.", "codeNodeEditor.completer.luxon.instanceMethods.invalidReason": "Returns an error code if this DateTime is invalid, or null if the DateTime is valid.", "codeNodeEditor.completer.luxon.instanceMethods.isInDST": "Whether the DateTime is in daylight saving time.", "codeNodeEditor.completer.luxon.instanceMethods.isInLeapYear": "Whether the DateTime is in a leap year.", "codeNodeEditor.completer.luxon.instanceMethods.isOffsetFixed": "Get whether this zone's offset ever changes, as in a DST.", "codeNodeEditor.completer.luxon.instanceMethods.isValid": "Returns whether the DateTime is valid. Invalid DateTimes occur when The DateTime was created from invalid calendar information, such as the 13th month or February 30. The DateTime was created by an operation on another invalid date.", "codeNodeEditor.completer.luxon.instanceMethods.locale": "The locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.max": "Return the max of several date times.", "codeNodeEditor.completer.luxon.instanceMethods.millisecond": "The millisecond of the second (0-999).", "codeNodeEditor.completer.luxon.instanceMethods.min": "Return the min of several date times", "codeNodeEditor.completer.luxon.instanceMethods.minus": "Subtract hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds.", "codeNodeEditor.completer.luxon.instanceMethods.minute": "The minute of the hour (0-59).", "codeNodeEditor.completer.luxon.instanceMethods.month": "The month (1-12).", "codeNodeEditor.completer.luxon.instanceMethods.monthLong": "The textual long month name, e.g. 'October'. Defaults to the system's locale if unspecified.", "codeNodeEditor.completer.luxon.instanceMethods.monthShort": "The textual abbreviated month name, e.g. 'Oct'. Defaults to the system's locale if unspecified.", "codeNodeEditor.completer.luxon.instanceMethods.numberingSystem": "Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.offset": "Get the UTC offset of this DateTime in minutes", "codeNodeEditor.completer.luxon.instanceMethods.offsetNameLong": "Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".", "codeNodeEditor.completer.luxon.instanceMethods.offsetNameShort": "Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".'", "codeNodeEditor.completer.luxon.instanceMethods.offsetNumber": "Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".'", "codeNodeEditor.completer.luxon.instanceMethods.ordinal": "Get the ordinal (meaning the day of the year).", "codeNodeEditor.completer.luxon.instanceMethods.outputCalendar": "Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.plus": "Add hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds.", "codeNodeEditor.completer.luxon.instanceMethods.quarter": "The quarter of the year (1-4).", "codeNodeEditor.completer.luxon.instanceMethods.reconfigure": "'Set' the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.resolvedLocaleOptions": "Returns the resolved Intl options for this DateTime. This is useful in understanding the behavior of formatting methods.", "codeNodeEditor.completer.luxon.instanceMethods.second": "The second of the minute (0-59).", "codeNodeEditor.completer.luxon.instanceMethods.set": "Assigns new values to specified units of the DateTime. To round a DateTime, see also <code>startOf()</code> and <code>endOf()</code>.", "codeNodeEditor.completer.luxon.instanceMethods.set.args.values": "An object containing the units to set and corresponding values to assign. Possible keys are <code>year</code>, <code>month</code>, <code>day</code>, <code>hour</code>, <code>minute</code>, <code>second</code> and <code>millsecond</code>.", "codeNodeEditor.completer.luxon.instanceMethods.setLocale": "Sets the locale, which determines the language and formatting for the DateTime. Useful when generating a textual representation of the DateTime, e.g. with <code>format()</code> or <code>toLocaleString()</code>.", "codeNodeEditor.completer.luxon.instanceMethods.setLocale.args.locale": "The locale to set, e.g. 'en-GB' for British English or 'pt-BR' for Brazilian Portuguese. <a target=\"blank\" href=”https://www.localeplanet.com/icu/”>List</a> (unofficial)", "codeNodeEditor.completer.luxon.instanceMethods.setZone": "Converts the DateTime to the given time zone. The DateTime still represents the same moment unless specified in the options. See also <code>toLocal()</code> and <code>toUTC()</code>.", "codeNodeEditor.completer.luxon.instanceMethods.setZone.args.zone": "A zone identifier, either in the format <code>'America/New_York'</code>, <code>'UTC+3'</code>, or the strings <code>'local'</code> or <code>'utc'</code>. <code>'local'</code> is the workflow's local time zone, this can be changed in workflow settings.", "codeNodeEditor.completer.luxon.instanceMethods.setZone.args.opts": "Options that affect the output. Possible properties:\n<code>keepCalendarTime</code> (boolean): Whether to keep the time the same and only change the offset. Defaults to false.", "codeNodeEditor.completer.luxon.instanceMethods.startOf": "Rounds the DateTime down to the beginning of one of its units, e.g. the start of the month", "codeNodeEditor.completer.luxon.instanceMethods.startOf.args.unit": "The unit to round to the beginning of. One of <code>year</code>, <code>quarter</code>, <code>month</code>, <code>week</code>, <code>day</code>, <code>hour</code>, <code>minute</code>, <code>second</code>, or <code>millisecond</code>.", "codeNodeEditor.completer.luxon.instanceMethods.startOf.args.opts": "Object with options that affect the output. Possible properties:\n<code>useLocaleWeeks</code> (boolean): Whether to use the locale when calculating the start of the week. Defaults to false.", "codeNodeEditor.completer.luxon.instanceMethods.toBSON": "Returns a BSON serializable equivalent to this DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.toFormat": "Returns a string representation of this DateTime formatted according to the specified format string.", "codeNodeEditor.completer.luxon.instanceMethods.toHTTP": "Returns a string representation of this DateTime appropriate for use in HTTP headers.", "codeNodeEditor.completer.luxon.instanceMethods.toISO": "Returns an ISO 8601-compliant string representation of this DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.toISO.args.opts": "Configuration options. See <a target=\"blank\" href=\"https://moment.github.io/luxon/api-docs/index.html#datetimetoiso\">Luxon docs</a> for more info.", "codeNodeEditor.completer.luxon.instanceMethods.toISODate": "Returns an ISO 8601-compliant string representation of this DateTime's date component.", "codeNodeEditor.completer.luxon.instanceMethods.toISOTime": "Returns an ISO 8601-compliant string representation of this DateTime's time component.", "codeNodeEditor.completer.luxon.instanceMethods.toISOWeekDate": "Returns an ISO 8601-compliant string representation of this DateTime's week date.", "codeNodeEditor.completer.luxon.instanceMethods.toJSON": "Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.", "codeNodeEditor.completer.luxon.instanceMethods.toJsDate": "Returns a JavaScript Date equivalent to this DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.toLocal": "Converts a DateTime to the workflow's local time zone. The DateTime still represents the same moment unless specified in the parameters. The workflow's time zone can be set in the workflow settings.", "codeNodeEditor.completer.luxon.instanceMethods.toLocal.example": "if time zone is Europe/Berlin", "codeNodeEditor.completer.luxon.instanceMethods.toLocaleParts": "Returns an array of format \"parts\", meaning individual tokens along with metadata.", "codeNodeEditor.completer.luxon.instanceMethods.toLocaleString": "Returns a localized string representing the DateTime, i.e. in the language and format corresponding to its locale. Defaults to the system's locale if none specified.", "codeNodeEditor.completer.luxon.instanceMethods.toLocaleString.args.opts": "Configuration options for the rendering. See <a href=”https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#parameters”>Intl.DateTimeFormat</a> for a full list. Defaults to rendering a short date.", "codeNodeEditor.completer.luxon.instanceMethods.toLocaleString.example": "Configuration options for the rendering. See <a href=”https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/DateTimeFormat#parameters”>Intl.DateTimeFormat</a> for a full list. Defaults to rendering a short date.", "codeNodeEditor.completer.luxon.instanceMethods.toMillis": "Returns a Unix timestamp in milliseconds (the number elapsed since 1st Jan 1970)", "codeNodeEditor.completer.luxon.instanceMethods.toObject": "Returns a JavaScript object with this DateTime's year, month, day, and so on.", "codeNodeEditor.completer.luxon.instanceMethods.toRFC2822": "Returns an RFC 2822-compatible string representation of this DateTime, always in UTC.", "codeNodeEditor.completer.luxon.instanceMethods.toRelative": "Returns a textual representation of the time relative to now, e.g. 'in two days'. Rounds down by default.", "codeNodeEditor.completer.luxon.instanceMethods.toRelative.args.opts": "Options that affect the output. Possible properties:\n<code>unit</code> = the unit to default to (<code>years</code>, <code>months</code>, <code>days</code>, etc.).\n<code>locale</code> = the language and formatting to use (e.g. <code>de</code>, <code>fr</code>)", "codeNodeEditor.completer.luxon.instanceMethods.toRelativeCalendar": "Returns a string representation of this date relative to today, such as '\"'yesterday' or 'next month'.", "codeNodeEditor.completer.luxon.instanceMethods.toSQL": "Returns a string representation of this DateTime appropriate for use in SQL DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.toSQLDate": "Returns a string representation of this DateTime appropriate for use in SQL Date.", "codeNodeEditor.completer.luxon.instanceMethods.toSQLTime": "Returns a string representation of this DateTime appropriate for use in SQL Time.", "codeNodeEditor.completer.luxon.instanceMethods.toSeconds": "Returns a Unix timestamp in seconds (the number elapsed since 1st Jan 1970)", "codeNodeEditor.completer.luxon.instanceMethods.toString": "Returns a string representation of the DateTime. Similar to <code>toISO()</code>. For more formatting options, see <code>format()</code> or <code>toLocaleString()</code>.", "codeNodeEditor.completer.luxon.instanceMethods.toUTC": "Converts a DateTime to the UTC time zone. The DateTime still represents the same moment unless specified in the parameters. Use <code>setZone()</code> to convert to other zones.", "codeNodeEditor.completer.luxon.instanceMethods.toUTC.args.offset": "An offset from UTC in minutes", "codeNodeEditor.completer.luxon.instanceMethods.toUTC.args.opts": "Options that affect the output. Possible properties:\n<code>keepCalendarTime</code> (boolean): Whether to keep the time the same and only change the offset. Defaults to false.", "codeNodeEditor.completer.luxon.instanceMethods.toUnixInteger": "Returns the epoch seconds (as a whole number) of this DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.until": "Return an Interval spanning between this DateTime and another DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.valueOf": "Returns the epoch milliseconds of this DateTime.", "codeNodeEditor.completer.luxon.instanceMethods.weekNumber": "The week number of the year (1-52ish).", "codeNodeEditor.completer.luxon.instanceMethods.weekYear": "Get the week year.", "codeNodeEditor.completer.luxon.instanceMethods.weekday": "The day of the week. 1 is Monday and 7 is Sunday.", "codeNodeEditor.completer.luxon.instanceMethods.weekdayLong": "The textual long weekday name, e.g. 'Wednesday'. Defaults to the system's locale if unspecified.", "codeNodeEditor.completer.luxon.instanceMethods.weekdayShort": "The textual abbreviated weekday name, e.g. 'Wed'. Defaults to the system's locale if unspecified.", "codeNodeEditor.completer.luxon.instanceMethods.weeksInWeekYear": "Returns the number of weeks in this DateTime's year.", "codeNodeEditor.completer.luxon.instanceMethods.year": "The year.", "codeNodeEditor.completer.luxon.instanceMethods.zone": "The time zone associated with the DateTime", "codeNodeEditor.completer.luxon.instanceMethods.zoneName": "Get the name of the time zone.", "codeNodeEditor.completer.selector.all": "@:_reusableBaseText.codeNodeEditor.completer.all", "codeNodeEditor.completer.selector.context": "Extra data about the node", "codeNodeEditor.completer.selector.first": "@:_reusableBaseText.codeNodeEditor.completer.first", "codeNodeEditor.completer.selector.item": "Returns the matching item, i.e. the one used to produce the current item in the current node.", "codeNodeEditor.completer.selector.args.branchIndex": "The output branch of the node to use. Defaults to the first branch (index 0)", "codeNodeEditor.completer.selector.args.runIndex": "The run of the node to use. Defaults to the first run (index 0)", "codeNodeEditor.completer.selector.itemMatching": "@:_reusableBaseText.codeNodeEditor.completer.itemMatching", "codeNodeEditor.completer.selector.itemMatching.args.currentItemIndex": "The index of the item in the current node to be matched with.", "codeNodeEditor.completer.selector.last": "@:_reusableBaseText.codeNodeEditor.completer.last", "codeNodeEditor.completer.selector.params": "The configuration settings of the given node. These are the parameters you fill out within the node's UI (e.g. its operation).", "codeNodeEditor.completer.selector.isExecuted": "Is <code>true</code> if the node has executed, <code>false</code> otherwise", "codeNodeEditor.completer.section.input": "Input", "codeNodeEditor.completer.section.prevNodes": "Earlier nodes", "codeNodeEditor.completer.section.metadata": "<PERSON><PERSON><PERSON>", "codeNodeEditor.completer.section.fields": "Fields", "codeNodeEditor.completer.section.properties": "Properties", "codeNodeEditor.completer.section.methods": "Methods", "codeNodeEditor.completer.section.otherMethods": "Other methods", "codeNodeEditor.completer.section.recommended": "Suggested", "codeNodeEditor.completer.section.recommendedMethods": "Suggested methods", "codeNodeEditor.completer.section.other": "Other", "codeNodeEditor.completer.section.edit": "Edit", "codeNodeEditor.completer.section.query": "Query", "codeNodeEditor.completer.section.format": "Format", "codeNodeEditor.completer.section.component": "Component", "codeNodeEditor.completer.section.case": "Case", "codeNodeEditor.completer.section.cast": "Cast", "codeNodeEditor.completer.section.compare": "Compare", "codeNodeEditor.completer.section.validation": "Validate", "codeNodeEditor.linter.allItems.firstOrLastCalledWithArg": "expects no argument.", "codeNodeEditor.linter.allItems.emptyReturn": "Code doesn't return items properly. Please return an array of objects, one for each item you would like to output.", "codeNodeEditor.linter.allItems.itemCall": "`item` is a property to access, not a method to call. Did you mean `.item` without brackets?", "codeNodeEditor.linter.allItems.itemMatchingNoArg": "`.itemMatching()` expects an item index to be passed in as its argument.", "codeNodeEditor.linter.allItems.unavailableItem": "Legacy `item` is only available in the 'Run Once for Each Item' mode.", "codeNodeEditor.linter.allItems.unavailableProperty": "`.item` is only available in the 'Run Once for Each Item' mode. Use `.first()` instead.", "codeNodeEditor.linter.allItems.unavailableVar": "is only available in the 'Run Once for Each Item' mode.", "codeNodeEditor.linter.bothModes.directAccess.firstOrLastCall": "@:_reusableBaseText.codeNodeEditor.linter.useJson", "codeNodeEditor.linter.bothModes.directAccess.itemProperty": "@:_reusableBaseText.codeNodeEditor.linter.useJson", "codeNodeEditor.linter.bothModes.varDeclaration.itemProperty": "@:_reusableBaseText.codeNodeEditor.linter.useJson", "codeNodeEditor.linter.bothModes.varDeclaration.itemSubproperty": "@:_reusableBaseText.codeNodeEditor.linter.useJson", "codeNodeEditor.linter.eachItem.emptyReturn": "Code doesn't return an object. Please return an object representing the output item", "codeNodeEditor.linter.eachItem.legacyItemAccess": "`item` is a legacy var. Consider using `$input.item`", "codeNodeEditor.linter.eachItem.returnArray": "Code doesn't return an object. <PERSON><PERSON><PERSON> found instead. Please return an object representing the output item", "codeNodeEditor.linter.eachItem.unavailableItems": "Legacy `items` is only available in the 'Run Once for All Items' mode.", "codeNodeEditor.linter.eachItem.unavailableMethod": "Method `$input.{method}()` is only available in the 'Run Once for All Items' mode.", "codeNodeEditor.linter.eachItem.preferFirst": "Prefer `.first()` over `.item` so n8n can optimize execution", "codeNodeEditor.linter.bothModes.syntaxError": "Syntax error", "codeNodeEditor.linter.bothModes.dollarSignVariable": "Use a string literal instead of a variable so n8n can optimize execution.", "codeNodeEditor.askAi.placeholder": "Tell AI what you want the code to achieve. You can reference input data fields using dot notation (e.g. user.email)", "codeNodeEditor.askAi.intro": "Hey AI, generate JavaScript code that...", "codeNodeEditor.askAi.help": "Help", "codeNodeEditor.askAi.generateCode": "Generate Code", "codeNodeEditor.askAi.noInputData": "You can generate code once this node has incoming input data (from a node earlier in your workflow)", "codeNodeEditor.askAi.sureLeaveTab": "Are you sure you want to switch tab? The code generation will stop", "codeNodeEditor.askAi.areYouSure": "Are you sure?", "codeNodeEditor.askAi.switchTab": "Switch Tab", "codeNodeEditor.askAi.noPrompt": "First enter a prompt above before generating code", "codeNodeEditor.askAi.onlyAllItemsMode": "Ask AI generation works only in 'Run Once for All Items' mode", "codeNodeEditor.askAi.promptTooShort": "Enter a minimum of {minLength} characters before attempting to generate code", "codeNodeEditor.askAi.generateCodeAndReplace": "Generate and Replace Code", "codeNodeEditor.askAi.replaceCurrentCode": "Replace current code?", "codeNodeEditor.askAi.areYouSureToReplace": "Are you sure you want to generate new code? Your current code will be replaced.", "codeNodeEditor.askAi.loadingPhrase0": "AI cogs whirring, almost there…", "codeNodeEditor.askAi.loadingPhrase1": "up up down down left right b a start…", "codeNodeEditor.askAi.loadingPhrase2": "Consulting <PERSON>…", "codeNodeEditor.askAi.loadingPhrase3": "Gathering bytes and pieces…", "codeNodeEditor.askAi.loadingPhrase4": "Checking if another AI knows the answer…", "codeNodeEditor.askAi.loadingPhrase5": "Checking on Stack Overflow…", "codeNodeEditor.askAi.loadingPhrase6": "Crunching data, AI-style…", "codeNodeEditor.askAi.loadingPhrase7": "Stand by, AI magic at work…", "codeNodeEditor.askAi.generationCompleted": "✨ Code generation completed", "codeNodeEditor.askAi.generationFailed": "Code generation failed", "codeNodeEditor.askAi.generationFailedUnknown": "Code generation failed due to an unknown reason. Try again in a few minutes", "codeNodeEditor.askAi.generationFailedWithReason": "Code generation failed with error: {error}. Try again in a few minutes", "codeNodeEditor.askAi.generationFailedDown": "We're sorry, our AI service is currently unavailable. Please try again later. If the problem persists, contact support.", "codeNodeEditor.askAi.generationFailedRate": "We've hit our rate limit with our AI partner (too many requests). Please wait a minute before trying again.", "codeNodeEditor.askAi.generationFailedTooLarge": "Your workflow data is too large for AI to process. Simplify the data being sent into the Code node and retry.", "codeNodeEditor.tabs.askAi": "✨ Ask AI", "codeNodeEditor.tabs.code": "Code", "codeNodeEditor.examples": "Examples", "codeNodeEditor.parameters": "Parameters", "codeNodeEditor.optional": "optional", "codeNodeEditor.defaultsTo": "Defaults to {default}.", "collectionParameter.choose": "Choose...", "collectionParameter.noProperties": "No properties", "credentialEdit.credentialConfig.accountConnected": "Account connected", "credentialEdit.credentialConfig.clickToCopy": "Click To Copy", "credentialEdit.credentialConfig.connectionTestedSuccessfully": "Connection tested successfully", "credentialEdit.credentialConfig.couldntConnectWithTheseSettings": "Couldn’t connect with these settings", "credentialEdit.credentialConfig.couldntConnectWithTheseSettings.sharee": "Problem with connection settings. {owner} may be able to fix this", "credentialEdit.credentialConfig.needHelpFillingOutTheseFields": "Need help filling out these fields?", "credentialEdit.credentialConfig.oAuthRedirectUrl": "OAuth Redirect URL", "credentialEdit.credentialConfig.openDocs": "Open docs", "credentialEdit.credentialConfig.pleaseCheckTheErrorsBelow": "Please check the errors below", "credentialEdit.credentialConfig.pleaseCheckTheErrorsBelow.sharee": "Problem with connection settings. {owner} may be able to fix this", "credentialEdit.credentialConfig.reconnect": "Reconnect", "credentialEdit.credentialConfig.reconnectOAuth2Credential": "Reconnect OAuth2 Credential", "credentialEdit.credentialConfig.redirectUrlCopiedToClipboard": "Redirect URL copied to clipboard", "credentialEdit.credentialConfig.retry": "Retry", "credentialEdit.credentialConfig.retryCredentialTest": "Retry credential test", "credentialEdit.credentialConfig.retrying": "Retrying", "credentialEdit.credentialConfig.subtitle": "In {appName}, use the URL above when prompted to enter an OAuth callback or redirect URL", "credentialEdit.credentialConfig.theServiceYouReConnectingTo": "the service you're connecting to", "credentialEdit.credentialConfig.missingCredentialType": "This credential's type isn't available. This usually happens when a previously installed community or custom node was uninstalled.", "credentialEdit.credentialConfig.authTypeSelectorLabel": "Connect using", "credentialEdit.credentialConfig.authTypeSelectorTooltip": "The authentication method to use for the connection", "credentialEdit.credentialConfig.recommendedAuthTypeSuffix": "(recommended)", "credentialEdit.credentialConfig.externalSecrets": "Enterprise plan users can pull in credentials from external vaults.", "credentialEdit.credentialConfig.externalSecrets.moreInfo": "More info", "credentialEdit.credentialEdit.confirmMessage.beforeClose1.cancelButtonText": "Close", "credentialEdit.credentialEdit.confirmMessage.beforeClose1.confirmButtonText": "Keep Editing", "credentialEdit.credentialEdit.confirmMessage.beforeClose1.headline": "Close without saving?", "credentialEdit.credentialEdit.confirmMessage.beforeClose1.message": "Are you sure you want to throw away the changes you made to the {credentialDisplayName} credential?", "credentialEdit.credentialEdit.confirmMessage.beforeClose2.cancelButtonText": "Close", "credentialEdit.credentialEdit.confirmMessage.beforeClose2.confirmButtonText": "Keep Editing", "credentialEdit.credentialEdit.confirmMessage.beforeClose2.headline": "Close without connecting?", "credentialEdit.credentialEdit.confirmMessage.beforeClose2.message": "You need to connect your credential for it to work", "credentialEdit.credentialEdit.confirmMessage.deleteCredential.cancelButtonText": "", "credentialEdit.credentialEdit.confirmMessage.deleteCredential.confirmButtonText": "Yes, delete", "credentialEdit.credentialEdit.confirmMessage.deleteCredential.headline": "Delete Credential?", "credentialEdit.credentialEdit.confirmMessage.deleteCredential.message": "Are you sure you want to delete \"{savedCredentialName}\"? This may break any workflows that use it.", "credentialEdit.credentialEdit.connection": "Connection", "credentialEdit.credentialEdit.sharing": "Sharing", "credentialEdit.credentialEdit.couldNotFindCredentialOfType": "Could not find credential of type", "credentialEdit.credentialEdit.couldNotFindCredentialWithId": "Could not find credential with ID", "credentialEdit.credentialEdit.delete": "Delete", "credentialEdit.credentialEdit.details": "Details", "credentialEdit.credentialEdit.saving": "Saving", "credentialEdit.credentialEdit.showError.createCredential.title": "Problem creating credential", "credentialEdit.credentialEdit.showError.deleteCredential.title": "Problem deleting credential", "credentialEdit.credentialEdit.showError.generateAuthorizationUrl.message": "There was a problem generating the authorization URL", "credentialEdit.credentialEdit.showError.generateAuthorizationUrl.title": "OAuth Authorization Error", "credentialEdit.credentialEdit.showError.loadCredential.title": "Problem loading credential", "credentialEdit.credentialEdit.showError.updateCredential.title": "Problem updating credential", "credentialEdit.credentialEdit.showMessage.title": "Credential deleted", "credentialEdit.credentialEdit.testing": "Testing", "credentialEdit.credentialEdit.info.sharee": "Only {credentialOwnerName} can edit this connection", "credentialEdit.credentialInfo.allowUseBy": "Allow use by", "credentialEdit.credentialInfo.created": "Created", "credentialEdit.credentialInfo.id": "ID", "credentialEdit.credentialInfo.lastModified": "Last modified", "credentialEdit.credentialEdit.setupGuide": "Setup guide", "credentialEdit.credentialEdit.docs": "Docs", "credentialEdit.oAuthButton.connectMyAccount": "Connect my account", "credentialEdit.oAuthButton.signInWithGoogle": "Sign in with Google", "credentialEdit.credentialSharing.info.owner": "Sharing a credential allows people to use it in their workflows. They cannot access credential details.", "credentialEdit.credentialSharing.info.sharee.team": "Only users with credential sharing permission can change who this credential is shared with", "credentialEdit.credentialSharing.info.sharee.personal": "Only {credentialOwnerName} or users with credential sharing permission can change who this credential is shared with", "credentialEdit.credentialSharing.info.sharee.fallback": "the owner", "credentialEdit.credentialSharing.list.delete": "Remove", "credentialEdit.credentialSharing.list.delete.confirm.title": "Remove access?", "credentialEdit.credentialSharing.list.delete.confirm.message": "This may break any workflows in which {name} has used this credential", "credentialEdit.credentialSharing.list.delete.confirm.confirmButtonText": "Remove", "credentialEdit.credentialSharing.list.delete.confirm.cancelButtonText": "Cancel", "credentialEdit.credentialSharing.role.user": "User", "credentialSelectModal.addNewCredential": "Add new credential", "credentialSelectModal.continue": "Continue", "credentialSelectModal.searchForApp": "Search for app...", "credentialSelectModal.selectAnAppOrServiceToConnectTo": "Select an app or service to connect to", "credentialsList.addNew": "Add New", "credentialsList.confirmMessage.cancelButtonText": "", "credentialsList.confirmMessage.confirmButtonText": "Yes, delete", "credentialsList.confirmMessage.headline": "Delete Credential?", "credentialsList.confirmMessage.message": "Are you sure you want to delete {credentialName}?", "credentialsList.created": "Created", "credentialsList.credentials": "Credentials", "credentialsList.deleteCredential": "Delete Credential", "credentialsList.editCredential": "Edit Credential", "credentialsList.errorLoadingCredentials": "Error loading credentials", "credentialsList.name": "@:_reusableBaseText.name", "credentialsList.operations": "Operations", "credentialsList.showError.deleteCredential.title": "Problem deleting credential", "credentialsList.showMessage.title": "Credential deleted", "credentialsList.type": "Type", "credentialsList.updated": "Updated", "credentialsList.yourSavedCredentials": "Your saved credentials", "credentials.heading": "Credentials", "credentials.add": "Add credential", "credentials.project.add": "Add credential to project", "credentials.empty.heading": "{name}, let's set up a credential", "credentials.empty.heading.userNotSetup": "Set up a credential", "credentials.empty.description": "Credentials let workflows interact with your apps and services", "credentials.empty.button": "Add first credential", "credentials.empty.button.disabled.tooltip": "Your current role in the project does not allow you to create credentials", "credentials.item.open": "Open", "credentials.item.delete": "Delete", "credentials.item.move": "Change owner", "credentials.item.updated": "Last updated", "credentials.item.created": "Created", "credentials.item.owner": "Owner", "credentials.item.readonly": "Read only", "credentials.item.needsSetup": "Needs first setup", "credentials.search.placeholder": "Search credentials...", "credentials.filters.type": "Type", "credentials.filters.setup": "Needs first setup", "credentials.filters.status": "Status", "credentials.filters.active": "Some credentials may be hidden since filters are applied.", "credentials.filters.active.reset": "Remove filters", "credentials.sort.lastUpdated": "Sort by last updated", "credentials.sort.lastCreated": "Sort by last created", "credentials.sort.nameAsc": "Sort by name (A-Z)", "credentials.sort.nameDesc": "Sort by name (Z-A)", "credentials.noResults": "No credentials found", "credentials.noResults.withSearch.switchToShared.preamble": "some credentials may be", "credentials.noResults.withSearch.switchToShared.link": "hidden", "credentials.create.personal.toast.title": "Credential successfully created", "credentials.create.personal.toast.text": "This credential has been created inside your personal space.", "credentials.create.project.toast.title": "Credential successfully created in {projectName}", "credentials.create.project.toast.text": "All members from {projectName} will have access to this credential.", "dataDisplay.needHelp": "Need help?", "dataDisplay.nodeDocumentation": "Node Documentation", "dataDisplay.openDocumentationFor": "Open {nodeTypeDisplayName} documentation", "dataMapping.dragColumnToFieldHint": "Drag onto a field to map column to that field", "dataMapping.dragFromPreviousHint": "Map data from previous nodes to <b>{name}</b> by first clicking this button", "dataMapping.success.title": "You just mapped some data!", "dataMapping.success.moreInfo": "Check out our <a href=\"https://docs.n8n.io/data/data-mapping\" target=\"_blank\">docs</a> for more details on mapping data in n8n", "dataMapping.tableView.tableColumnsExceeded": "Some columns are hidden", "dataMapping.tableView.tableColumnsExceeded.tooltip": "Your data has more than {columnLimit} columns so some are hidden. Switch to {link} to see all data.", "dataMapping.tableView.tableColumnsExceeded.tooltip.link": "JSON view", "dataMapping.tableView.columnCollapsing": "Collapse rows", "dataMapping.tableView.columnCollapsing.tooltip": "Collapse rows (to compare values in this column)", "dataMapping.schemaView.emptyData": "No fields - node executed, but no items were sent on this branch", "dataMapping.schemaView.emptySchema": "No fields - item(s) exist, but they're empty", "dataMapping.schemaView.emptySchemaWithBinary": "Only binary data exists. View it using the 'Binary' tab", "dataMapping.schemaView.executeSchema": "{link} to see schema", "dataMapping.schemaView.disabled": "This node is disabled and will just pass data through", "dataMapping.schemaView.noMatches": "No results for '{search}'", "dataMapping.schemaView.preview": "Usually outputs the following fields. Execute the node to see the actual ones. {link}", "dataMapping.schemaView.previewExtraFields": "There may be more fields. Execute the node to be sure.", "dataMapping.schemaView.previewNode": "Preview", "dataMapping.schemaView.variablesContextTitle": "Variables and context", "dataMapping.schemaView.execution.resumeUrl": "The URL for resuming a 'Wait' node", "dataMapping.schemaView.variablesUpgrade": "Set global variables and use them across workflows with the Pro or Enterprise plan. <a href=\"https://docs.n8n.io/environments/variables/\" target=\"_blank\">Details</a>", "dataMapping.schemaView.variablesEmpty": "Create variables that can be used across workflows <a href=\"/variables\" target=\"_blank\">here</a>", "displayWithChange.cancelEdit": "Cancel Edit", "displayWithChange.clickToChange": "Click to Change", "displayWithChange.setValue": "Set Value", "duplicateWorkflowDialog.cancel": "@:_reusableBaseText.cancel", "duplicateWorkflowDialog.chooseOrCreateATag": "Choose or create a tag", "duplicateWorkflowDialog.duplicateWorkflow": "Duplicate Workflow", "duplicateWorkflowDialog.enterWorkflowName": "Enter workflow name", "duplicateWorkflowDialog.save": "Duplicate", "duplicateWorkflowDialog.errors.missingName.title": "Name missing", "duplicateWorkflowDialog.errors.missingName.message": "Please enter a name.", "duplicateWorkflowDialog.errors.forbidden.title": "Duplicate workflow failed", "duplicateWorkflowDialog.errors.forbidden.message": "This action is forbidden. Do you have the correct permissions?", "duplicateWorkflowDialog.errors.generic.title": "Duplicate workflow failed", "editor.mainHeader.githubButton.label": "Star n8n-io/n8n on GitHub", "error": "Error", "error.goBack": "Go back", "error.pageNotFound": "Oops, couldn’t find that", "error.entityNotFound.title": "{entity} not found", "error.entityNotFound.text": "We couldn’t find the {entity} you were looking for. Make sure you have the correct URL.", "error.entityNotFound.action": "Go to overview", "error.entityUnAuthorized.title": "You need access", "error.entityUnAuthorized.content": "You don't have permission to view this {entity}. Please contact the person who shared this link to request access.", "executions.ExecutionStatus": "Execution status", "executions.concurrency.docsLink": "https://docs.n8n.io/hosting/scaling/concurrency-control/", "executionDetails.additionalActions": "Additional Actions", "executionDetails.confirmMessage.confirmButtonText": "Yes, delete", "executionDetails.confirmMessage.headline": "Delete Execution?", "executionDetails.confirmMessage.message": "Are you sure that you want to delete the current execution?", "executionDetails.confirmMessage.annotationsNote": "By deleting this you will also remove the associated annotation data.", "executionDetails.deleteExecution": "Delete this execution", "executionDetails.executionFailed": "Execution failed", "executionDetails.executionFailed.recoveredNodeTitle": "Can’t show data", "executionDetails.executionFailed.recoveredNodeMessage": "The execution was interrupted, so the data was not saved. Try fixing the workflow and re-executing.", "executionDetails.executionId": "Execution ID", "executionDetails.executionWaiting": "Execution waiting", "executionDetails.executionWasSuccessful": "Execution was successful", "executionDetails.of": "of", "executionDetails.at": "at", "executionDetails.newMessage": "Execution waiting in the queue.", "executionDetails.openWorkflow": "Open Workflow", "executionDetails.readOnly.readOnly": "Read only", "executionDetails.readOnly.youreViewingTheLogOf": "You're viewing the log of a previous execution. You cannot<br />\n\t\tmake changes since this execution already occurred. Make changes<br />\n\t\tto this workflow by clicking on its name on the left.", "executionDetails.retry": "Retry of execution", "executionDetails.runningTimeFinished": "in {time}", "executionDetails.runningTimeRunning": "for", "executionDetails.runningMessage": "Execution is running. It will show here once finished.", "executionDetails.startingSoon": "Starting soon", "executionDetails.workflow": "workflow", "executionsLandingPage.emptyState.noTrigger.heading": "Set up the first step. Then execute your workflow", "executionsLandingPage.emptyState.noTrigger.buttonText": "Add first step...", "executionsLandingPage.clickExecutionMessage": "Click on an execution from the list to view it", "executionsLandingPage.emptyState.heading": "Nothing here yet", "executionsLandingPage.emptyState.message": "New workflow executions will show here", "executionsLandingPage.emptyState.accordion.title": "Which executions is this workflow saving?", "executionsLandingPage.emptyState.accordion.titleWarning": "Some executions won’t be saved", "executionsLandingPage.emptyState.accordion.productionExecutions": "Production executions", "executionsLandingPage.emptyState.accordion.testExecutions": "Test executions", "executionsLandingPage.emptyState.accordion.productionExecutionsWarningTooltip": "Not all production executions are being saved. Change this in the workflow's <a href=\"#\">settings</a>", "executionsLandingPage.emptyState.accordion.footer": "You can change this in", "executionsLandingPage.emptyState.accordion.footer.settingsLink": "Workflow settings", "executionsLandingPage.emptyState.accordion.footer.tooltipLink": "Save your workflow", "executionsLandingPage.emptyState.accordion.footer.tooltipText": "in order to access workflow settings", "executionsLandingPage.noResults": "No executions found", "executionsList.activeExecutions.none": "No active executions", "executionsList.activeExecutions.header": "{running}/{cap} active executions", "executionsList.activeExecutions.tooltip": "Current active executions: {running} out of {cap}. This instance is limited to {cap} concurrent production executions.", "executionsList.activeExecutions.evaluationNote": "Evaluation runs appear in the list of executions but do not count towards your execution concurrency.", "executionsList.allWorkflows": "All Workflows", "executionsList.anyStatus": "Any Status", "executionsList.autoRefresh": "Auto refresh", "executionsList.canceled": "Canceled", "executionsList.confirmMessage.cancelButtonText": "", "executionsList.confirmMessage.confirmButtonText": "Yes, delete", "executionsList.confirmMessage.headline": "Delete Executions?", "executionsList.confirmMessage.message": "Are you sure that you want to delete the {count} selected execution(s)?", "executionsList.confirmMessage.annotationsNote": "By deleting these executions you will also remove the associated annotation data.", "executionsList.confirmMessage.annotatedExecutionMessage": "By deleting this you will also remove the associated annotation data. Are you sure that you want to delete the selected execution?", "executionsList.clearSelection": "Clear selection", "executionsList.error": "Error", "executionsList.filters": "Filters", "executionsList.loadMore": "Load more", "executionsList.empty": "No executions", "executionsList.loadedAll": "No more executions to fetch", "executionsList.modes.error": "error", "executionsList.modes.integrated": "integrated", "executionsList.modes.manual": "manual", "executionsList.modes.retry": "retry", "executionsList.modes.trigger": "trigger", "executionsList.modes.webhook": "webhook", "executionsList.name": "@:_reusableBaseText.name", "executionsList.new": "Queued", "executionsList.openPastExecution": "Open Past Execution", "executionsList.retryExecution": "Retry execution", "executionsList.retryOf": "Retry of", "executionsList.retryWithCurrentlySavedWorkflow": "Retry with currently saved workflow (from node with error)", "executionsList.retryWithOriginalWorkflow": "Retry with original workflow (from node with error)", "executionsList.running": "Running", "executionsList.succeeded": "Succeeded", "executionsList.selectStatus": "Select Status", "executionsList.selectWorkflow": "Select Workflow", "executionsList.selected": "{count} execution selected: | {count} executions selected:", "executionsList.selectAll": "Select {count} finished execution | Select all {count} finished executions", "executionsList.test": "Test execution", "executionsList.evaluation": "Evaluation execution", "executionsList.showError.handleDeleteSelected.title": "Problem deleting executions", "executionsList.showError.loadMore.title": "Problem loading executions", "executionsList.showError.loadWorkflows.title": "Problem loading workflows", "executionsList.showError.refreshData.title": "Problem loading data", "executionsList.showError.retryExecution.title": "Problem with retry", "executionsList.showError.stopExecution.title": "Problem stopping execution", "executionsList.showMessage.handleDeleteSelected.title": "Execution deleted", "executionsList.showMessage.retryError.title": "Retry unsuccessful", "executionsList.showMessage.retrySuccess.title": "Retry successful", "executionsList.showMessage.retryWaiting.title": "Retry waiting", "executionsList.showMessage.retryCrashed.title": "<PERSON><PERSON> crashed", "executionsList.showMessage.retryCanceled.title": "Retry canceled", "executionsList.showMessage.retryRunning.title": "<PERSON><PERSON> running", "executionsList.showMessage.stopExecution.message": "Execution ID {activeExecutionId}", "executionsList.showMessage.stopExecution.title": "Execution stopped", "executionsList.startedAt": "Started", "executionsList.trigger": "Triggered by", "executionsList.runTime": "Run time", "executionsList.startingSoon": "Starting soon", "executionsList.started": "{date}, {time}", "executionsList.id": "Exec. ID", "executionsList.status": "Status", "executionsList.statusCanceled": "Canceled", "executionsList.statusText": "{status} in {time}", "executionsList.statusTextWithoutTime": "{status}", "executionsList.statusRunning": "{status} for {time}", "executionsList.statusWaiting": "{status} until {time}", "executionsList.statusUnknown": "Could not complete", "executionsList.stopExecution": "Stop Execution", "executionsList.success": "Success", "executionsList.successRetry": "Success retry", "executionsList.unknown": "Could not complete", "executionsList.unsavedWorkflow": "[UNSAVED WORKFLOW]", "executionsList.waiting": "Waiting", "executionsList.workflowExecutions": "Executions", "executionsList.view": "View", "executionsList.stop": "Stop", "executionsList.statusTooltipText.waitingForWebhook": "The workflow is waiting indefinitely for an incoming webhook call.", "executionsList.statusTooltipText.waitingForConcurrencyCapacity": "This execution will start once concurrency capacity is available. {instance}", "executionsList.statusTooltipText.waitingForConcurrencyCapacity.cloud": "Your plan is limited to {concurrencyCap} concurrent production executions. {link}", "executionsList.statusTooltipText.waitingForConcurrencyCapacity.self": "This instance is limited to {concurrencyCap} concurrent production executions. {link}", "executionsList.statusTooltipText.theWorkflowIsWaitingIndefinitely": "The workflow is waiting indefinitely for an incoming webhook call.", "executionsList.debug.button.copyToEditor": "Co<PERSON> to editor", "executionsList.debug.button.debugInEditor": "Debug in editor", "executionsList.debug.paywall.title": "Upgrade to access Debug In Editor", "executionsList.debug.paywall.content": "Debug in Editor allows you to debug a previous execution with the actual data pinned, right in your editor.", "executionsList.debug.paywall.subContent": "It's available on our Cloud plans, Enterprise and the", "executionsList.debug.paywall.link.text": "Registered Community Edition.", "executionsList.debug.paywall.link.url": "https://docs.n8n.io/hosting/community-edition-features/#registered-community-edition", "fromAiParametersModal.title": "Test {nodeName}", "fromAiParametersModal.execute": "Execute step", "fromAiParametersModal.description": "Provide the data that would normally come from the \"{parentNodeName}\" node", "fromAiParametersModal.cancel": "Cancel", "workerList.pageTitle": "Workers", "workerList.empty": "No workers are responding or available", "workerList.item.lastUpdated": "Last updated", "workerList.item.jobList.empty": "No current jobs", "workerList.item.jobListTitle": "Current Jobs", "workerList.item.netListTitle": "Network Interfaces", "workerList.item.chartsTitle": "Performance Monitoring", "workerList.item.copyAddressToClipboard": "Address copied to clipboard", "workerList.actionBox.title": "Available on the Enterprise plan", "workerList.actionBox.description": "View the current state of workers connected to your instance.", "workerList.actionBox.description.link": "More info", "workerList.actionBox.buttonText": "See plans", "workerList.docs.url": "https://docs.n8n.io/hosting/scaling/queue-mode/#view-running-workers", "executionSidebar.executionName": "Execution {id}", "executionSidebar.searchPlaceholder": "Search executions...", "executionView.onPaste.title": "Cannot paste here", "executionView.onPaste.message": "This view is read-only. Switch to <i>Workflow</i> tab to be able to edit the current workflow", "executionView.notFound.message": "Execution with id '{executionId}' could not be found!", "executionAnnotationView.data.notFound": "Show important data from executions here by adding an <a target=\"_blank\" href=\"https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executiondata/\">execution data</a> node to your workflow", "executionAnnotationView.vote.error": "Unable to save annotation vote", "executionAnnotationView.tag.error": "Unable to save annotation tags", "executionAnnotationView.addTag": "Add tag", "executionAnnotationView.chooseOrCreateATag": "Choose or create a tag", "executionsFilter.annotation.tags": "Execution tags", "executionsFilter.annotation.rating": "Rating", "executionsFilter.annotation.rating.all": "Any rating", "executionsFilter.annotation.rating.good": "Good", "executionsFilter.annotation.rating.bad": "Bad", "executionsFilter.annotation.selectVoteFilter": "Select Rating", "executionsFilter.selectStatus": "Select Status", "executionsFilter.selectWorkflow": "Select Workflow", "executionsFilter.start": "Execution start", "executionsFilter.startDate": "Earliest", "executionsFilter.endDate": "Latest", "executionsFilter.savedData": "Highlighted data", "executionsFilter.savedDataExactMatch": "Exact match", "executionsFilter.savedDataKey": "Key", "executionsFilter.savedDataKeyPlaceholder": "ID", "executionsFilter.savedDataValue": "Value", "executionsFilter.savedDataValuePlaceholder": "123", "executionsFilter.reset": "Reset all", "executionsFilter.customData.inputTooltip": "Upgrade plan to filter executions by custom data set at runtime. {link}", "executionsFilter.customData.inputTooltip.link": "View plans", "executionsFilter.customData.docsTooltip": "Filter executions by data you have saved in them using an ‘Execution Data’ node. {link}", "executionsFilter.customData.docsTooltip.link": "More info", "expressionEdit.anythingInside": "Anything inside ", "expressionEdit.isJavaScript": " is JavaScript.", "expressionEdit.learnMore": "Learn more", "expressionEdit.editExpression": "Edit Expression", "expressionEdit.expression": "Expression", "expressionEdit.resultOfItem1": "Result of item 1", "expressionEditor.uncalledFunction": "[this is a function, please add ()]", "expressionModalInput.empty": "[empty]", "expressionModalInput.undefined": "[undefined]", "expressionModalInput.null": "null", "expressionTip.noExecutionData": "Execute previous nodes to use input data", "expressionTip.typeDotPrimitive": "Type <code>.</code> for data transformation options. <a target=\"_blank\" href=\"https://docs.n8n.io/code/builtin/data-transformation-functions/\">Learn more</a>", "expressionTip.typeDotObject": "Type <code>.</code> for data transformation options, or to access fields. <a target=\"_blank\" href=\"https://docs.n8n.io/code/builtin/data-transformation-functions/\">Learn more</a>", "expressionTip.javascript": "Anything inside <code>{'{{ }}'}</code> is JavaScript. <a target=\"_blank\" href=\"https://docs.n8n.io/code-examples/expressions/\">Learn more</a>", "expressionModalInput.noExecutionData": "Execute previous nodes for preview", "expressionModalInput.noNodeExecutionData": "Execute node ‘{node}’ for preview", "expressionModalInput.noInputConnection": "No input connected", "expressionModalInput.pairedItemConnectionError": "No path back to node", "expressionModalInput.pairedItemInvalidPinnedError": "Unpin node ‘{node}’ and execute", "expressionModalInput.pairedItemError": "Can’t determine which item to use", "expressionModalInput.pairedItemError.noRunData": "Can't determine which item to use - execute node for more info", "fixedCollectionParameter.choose": "Choose...", "fixedCollectionParameter.currentlyNoItemsExist": "Currently no items exist", "fixedCollectionParameter.deleteItem": "Delete item", "fixedCollectionParameter.dragItem": "Drag item", "fixedCollectionParameter.moveDown": "Move down", "fixedCollectionParameter.moveUp": "Move up", "forgotPassword": "Forgot my password", "forgotPassword.emailSentIfExists": "We’ve emailed {email} (if there’s a matching account)", "forgotPassword.getRecoveryLink": "Email me a recovery link", "forgotPassword.noSMTPToSendEmailWarning": "Please contact your admin. n8n isn’t set up to send email right now.", "forgotPassword.recoverPassword": "Recover password", "forgotPassword.recoveryEmailSent": "Recovery email sent", "forgotPassword.returnToSignIn": "Back to sign in", "forgotPassword.sendingEmailError": "Problem sending email", "forgotPassword.ldapUserPasswordResetUnavailable": "Please contact your LDAP administrator to reset your password", "forgotPassword.oidcUserPasswordResetUnavailable": "Please contact your OIDC administrator to reset your password", "forgotPassword.smtpErrorContactAdministrator": "Please contact your administrator (problem with your SMTP setup)", "forgotPassword.tooManyRequests": "You’ve reached the password reset limit. Please try again in a few minutes.", "forms.resourceFiltersDropdown.filters": "Filters", "forms.resourceFiltersDropdown.owner": "Owner", "forms.resourceFiltersDropdown.owner.placeholder": "Filter by owner", "forms.resourceFiltersDropdown.reset": "Reset all", "folders.actions.create": "Create folder inside", "folders.actions.create.workflow": "Create workflow inside", "folders.actions.moveToFolder": "Move", "folders.add": "Add folder", "folders.add.here.message": "Create a new folder here", "folders.add.to.parent.message": "Create folder in \"{parent}\"", "folders.add.overview.community.message": "Folders available in your personal space", "folders.add.overview.withProjects.message": "Folders available in projects or your personal space", "folders.add.success.title": "Folder created", "folders.add.success.message": "Created new folder \"{folderName}\"<br><a href=\"{link}\">Open folder</a>", "folders.invalidName.empty.message": "Folder name cannot be empty", "folders.invalidName.tooLong.message": "Folder name cannot be longer than {maxLength} characters", "folders.invalidName.invalidCharacters.message": "Folder name cannot contain the following characters: {illegalChars}", "folders.invalidName.starts.with.dot..message": "Folder name cannot start with a dot", "folders.invalidName.only.dots.message": "Folder name cannot contain only dots", "folders.delete.confirm.title": "Delete \"{folderName}\"", "folders.delete.typeToConfirm": "delete {folderName}", "folders.delete.confirm.message": "Are to sure you want to delete this folder?", "folders.delete.success.message": "Folder deleted", "folder.delete.modal.confirmation": "What should we do with {folders} {workflows} in this folder?", "folder.count": "the {count} folder | the {count} folders", "workflow.count": "the {count} workflow | the {count} workflows", "folder.and.workflow.separator": "and", "folders.delete.action": "Archive all workflows and delete subfolders", "folders.delete.error.message": "Problem while deleting folder", "folders.delete.confirmation.message": "Type \"delete {folderName}\" to confirm", "folders.transfer.confirm.message": "Data transferred to \"{folderName}\"", "folders.transfer.action": "Transfer workflows and subfolders to another folder inside \"{projectName}\"", "folders.transfer.action.noProject": "Transfer workflows and subfolders to another folder", "folders.transfer.selectFolder": "Folder to transfer to", "folders.transfer.select.placeholder": "Select folder", "folders.rename.message": "Rename \"{folderName}\"", "folders.rename.error.title": "Problem renaming folder", "folders.rename.success.message": "Folder renamed", "folders.rename.placeholder": "Enter new folder name", "folders.not.found.message": "Folder not found", "folders.move.modal.folder.count": "{count} folder | {count} folders", "folders.move.modal.workflow.count": "{count} workflow | {count} workflows", "folders.move.modal.title": "Move {resourceTypeLabel} {folderName}", "folders.move.modal.description": "This will also move{folders}{workflows}.", "folders.move.modal.confirm": "Move {resourceTypeLabel}", "folders.move.modal.project.label": "Project or user", "folders.move.modal.project.placeholder": "Select a project or user", "folders.move.modal.folder.label": "Folder", "folders.move.modal.folder.placeholder": "Select a folder", "folders.move.modal.folder.noData.label": "No folders found", "folders.move.modal.message.usedCredentials.workflow": "Also share the {usedCredentials} used by this workflow to ensure it will continue to run correctly", "folders.move.modal.message.usedCredentials.folder": "Also share the {usedCredentials} used by these workflows to keep them running correctly", "folders.move.modal.message.usedCredentials.warning": "Workflow may not execute correctly if you choose not to share the credentials.", "folders.move.success.title": "Successfully moved folder", "folders.move.success.message": "<b>{folderName}</b> has been moved to <b>{newFolderName}</b>, along with all its workflows and subfolders.<br/><br/><a href=\"{link}\">View {newFolderName}</a>", "folders.move.success.messageNoAccess": "<b>{folderName}</b> has been moved to <b>{newFolderName}</b>, along with all its workflows and subfolders.", "folders.move.error.title": "Problem moving folder", "folders.move.workflow.error.title": "Problem moving workflow", "folders.move.workflow.success.title": "Successfully moved workflow", "folders.move.workflow.success.message": "<b>{workflowName}</b> has been moved to <b>{newFolderName}</b>.<br/><br/><a href=\"{link}\">View {newFolderName}</a>", "folders.move.workflow.success.messageNoAccess": "<b>{workflowName}</b> has been moved to <b>{newFolderName}</b>.", "folders.move.project.root.name": "No folder (project root)", "folders.open.error.title": "Problem opening folder", "folders.create.error.title": "Problem creating folder", "folders.empty.actionbox.title": "Nothing in folder \"{folderName}\" yet", "folders.registeredCommunity.cta.heading": "Get access to folders with registered community", "folders.breadcrumbs.noTruncated.message": "No hidden items in path", "generic.oauth1Api": "OAuth1 API", "generic.oauth2Api": "OAuth2 API", "genericHelpers.loading": "Loading", "genericHelpers.hrsShort": "h", "genericHelpers.min": "min", "genericHelpers.minShort": "m", "genericHelpers.sec": "sec", "genericHelpers.secShort": "s", "genericHelpers.millis": "ms", "readOnly.showMessage.executions.message": "Executions are read-only. Make changes from the <b>Workflow</b> tab.", "readOnly.showMessage.executions.title": "Cannot edit execution", "readOnlyEnv.showMessage.executions.message": "Executions are read-only.", "readOnlyEnv.showMessage.executions.title": "Cannot edit execution", "readOnlyEnv.showMessage.workflows.message": "Workflows are read-only in protected instances.", "readOnlyEnv.showMessage.workflows.title": "Cannot edit workflow", "readOnlyEnv.tooltip": "This is a protected instance where modifications are restricted. {link}", "readOnlyEnv.tooltip.link": "More info.", "readOnlyEnv.cantAdd.workflow": "You can't add new workflows to a protected n8n instance", "readOnlyEnv.cantAdd.credential": "You can't add new credentials to a protected n8n instance", "readOnlyEnv.cantAdd.project": "You can't add new projects to a protected n8n instance", "readOnlyEnv.cantAdd.any": "You can't create new workflows or credentials on a protected n8n instance", "readOnlyEnv.cantEditOrRun": "This workflow can't be edited or run manually because it's on a protected instance", "logs.overview.header.title": "Logs", "logs.overview.header.actions.clearExecution": "Clear execution", "logs.overview.header.actions.clearExecution.tooltip": "Clear execution data", "logs.overview.header.switch.details": "Details", "logs.overview.header.switch.overview": "Overview", "logs.overview.body.empty.message": "Nothing to display yet. Execute the workflow to see execution logs.", "logs.overview.body.empty.action": "Execute the workflow", "logs.overview.body.summaryText.for": "{status} for {time}", "logs.overview.body.summaryText.in": "{status} in {time}", "logs.overview.body.started": "Started {time}", "logs.overview.body.run": "Execute step", "logs.overview.body.open": "Open...", "logs.overview.body.toggleRow": "Toggle row", "logs.details.header.actions.input": "Input", "logs.details.header.actions.output": "Output", "logs.details.body.itemCount": "{count} item | {count} items", "logs.details.body.multipleInputs": "Multiple inputs. View them by {button}", "logs.details.body.multipleInputs.openingTheNode": "opening the node", "mainSidebar.aboutN8n": "About n8n", "mainSidebar.confirmMessage.workflowArchive.cancelButtonText": "", "mainSidebar.confirmMessage.workflowArchive.confirmButtonText": "Yes, archive", "mainSidebar.confirmMessage.workflowArchive.headline": "Archive Workflow?", "mainSidebar.confirmMessage.workflowArchive.message": "Are you sure that you want to archive '{workflowName}'?", "mainSidebar.confirmMessage.workflowDelete.cancelButtonText": "", "mainSidebar.confirmMessage.workflowDelete.confirmButtonText": "Yes, delete", "mainSidebar.confirmMessage.workflowDelete.headline": "Delete Workflow?", "mainSidebar.confirmMessage.workflowDelete.message": "Are you sure that you want to delete '{workflowName}' permanently?", "mainSidebar.credentials": "Credentials", "mainSidebar.variables": "Variables", "mainSidebar.help": "Help", "mainSidebar.helpMenuItems.course": "Course", "mainSidebar.helpMenuItems.documentation": "Documentation", "mainSidebar.helpMenuItems.forum": "Forum", "mainSidebar.helpMenuItems.quickstart": "Quickstart", "mainSidebar.helpMenuItems.reportBug": "Report a bug", "mainSidebar.new": "New", "mainSidebar.newTemplate": "New from template", "mainSidebar.open": "Open", "mainSidebar.prompt.cancel": "@:_reusableBaseText.cancel", "mainSidebar.prompt.import": "Import", "mainSidebar.prompt.importWorkflowFromUrl": "Import Workflow from URL", "mainSidebar.prompt.invalidUrl": "Invalid URL", "mainSidebar.prompt.workflowUrl": "Workflow URL", "mainSidebar.save": "@:_reusableBaseText.save", "mainSidebar.showError.stopExecution.title": "Problem stopping execution", "mainSidebar.showMessage.handleFileImport.message": "The file does not contain valid JSON data", "mainSidebar.showMessage.handleFileImport.title": "Could not import file", "mainSidebar.showMessage.handleSelect1.title": "Workflow '{workflowName}' deleted", "mainSidebar.showMessage.handleSelect2.title": "Workflow created", "mainSidebar.showMessage.handleSelect3.title": "Workflow created", "mainSidebar.showMessage.handleArchive.title": "Workflow '{workflowName}' archived", "mainSidebar.showMessage.handleUnarchive.title": "Workflow '{workflowName}' unarchived", "mainSidebar.showMessage.stopExecution.title": "Execution stopped", "mainSidebar.templates": "Templates", "mainSidebar.workflows": "Workflows", "mainSidebar.workflows.readOnlyEnv.tooltip": "Protected instances prevent editing workflows (recommended for production environments). {link}", "mainSidebar.workflows.readOnlyEnv.tooltip.link": "More info", "mainSidebar.executions": "Executions", "mainSidebar.workersView": "Workers", "mainSidebar.whatsNew": "What’s New", "mainSidebar.whatsNew.fullChangelog": "Full changelog", "menuActions.duplicate": "Duplicate", "menuActions.download": "Download", "menuActions.push": "Push to Git", "menuActions.importFromUrl": "Import from URL...", "menuActions.importFromFile": "Import from File...", "menuActions.delete": "Delete", "menuActions.archive": "Archive", "menuActions.unarchive": "Unarchive", "multipleParameter.addItem": "Add item", "multipleParameter.currentlyNoItemsExist": "Currently no items exist", "multipleParameter.deleteItem": "Delete item", "multipleParameter.moveDown": "Move down", "multipleParameter.moveUp": "Move up", "ndv.backToCanvas": "Back to canvas", "ndv.backToCanvas.waitingForTriggerWarning": "Waiting for a Trigger node to execute. Close this view to see the Workflow Canvas.", "ndv.close.tooltip": "Data stored, safe to close", "ndv.execute.testNode": "Execute step", "ndv.execute.testNode.description": "Runs the current node. <PERSON> also run previous nodes if they have not been run yet", "ndv.execute.generateCodeAndTestNode.description": "Generates code and then runs the current node", "ndv.execute.generateCode.message": "The instructions in '{nodeName}' have changed", "ndv.execute.generateCode.title": "Generating transformation code", "ndv.execute.executing": "Executing", "ndv.execute.fetchEvent": "Fetch Test Event", "ndv.execute.fixPrevious": "Fix previous node first", "ndv.execute.generatingCode": "Generating code from instructions", "ndv.execute.listenForTestEvent": "Listen for test event", "ndv.execute.testChat": "Test chat", "ndv.execute.testStep": "Execute step", "ndv.execute.stopListening": "Stop Listening", "ndv.execute.nodeIsDisabled": "Enable node to execute", "ndv.execute.requiredFieldsMissing": "Complete required fields first", "ndv.execute.stopWaitingForWebhook.error": "Problem deleting test webhook", "ndv.execute.workflowAlreadyRunning": "Workflow is already running", "ndv.execute.deactivated": "This node is deactivated and can't be run", "ndv.featureRequest": "I wish this node would...", "ndv.input": "Input", "ndv.input.nodeDistance": "{count} node back | {count} nodes back", "ndv.input.noNodesFound": "No nodes found", "ndv.input.mapping": "Mapping", "ndv.input.fromAI": "From AI", "ndv.input.parentNodes": "Parent nodes", "ndv.input.tooMuchData.title": "Display data?", "ndv.input.noOutputDataInBranch": "No input data in this branch", "ndv.input.noOutputDataInNode": "Node did not output any data. n8n stops executing the workflow when a node has no output data.", "ndv.input.noOutputData": "No data", "ndv.input.noOutputData.executePrevious": "Execute previous nodes", "ndv.input.noOutputData.title": "No input data yet", "ndv.input.noOutputData.v2.title": "No input data", "ndv.input.noOutputData.v2.description": "{link} to view input data", "ndv.input.noOutputData.v2.action": "Execute previous nodes", "ndv.input.noOutputData.v2.tooltip": "From the earliest node which is unexecuted, or is executed but has since been changed", "ndv.input.noOutputData.hint": "(From the earliest node that needs it {info} )", "ndv.input.noOutputData.hint.tooltip": "From the earliest node which is unexecuted, or is executed but has since been changed", "ndv.input.noOutputData.schemaPreviewHint": "switch to {schema} to use the schema preview", "ndv.input.noOutputData.or": "or", "ndv.input.executingPrevious": "Executing previous nodes...", "ndv.input.notConnected.title": "Wire me up", "ndv.input.notConnected.v2.title": "No input connected", "ndv.input.notConnected.v2.description": "This node can only receive input data if you connect it to another node. {link}", "ndv.input.notConnected.message": "This node can only receive input data if you connect it to another node.", "ndv.input.notConnected.learnMore": "Learn more", "ndv.input.disabled": "The '{nodeName}' node is disabled and won’t execute.", "ndv.input.disabled.cta": "Enable it", "ndv.input.rootNodeHasNotRun.title": "Parent node hasn’t run yet", "ndv.input.rootNodeHasNotRun.description": "Inputs that the parent node sends to this one will appear here. To map data in from previous nodes, use the {link} view.", "ndv.input.rootNodeHasNotRun.description.link": "mapping", "ndv.output": "Output", "ndv.output.ai.empty": "👈 Use these logs to see information on how the {node} node completed processing. You can click on a node to see the input it received and data it output.", "ndv.output.ai.waiting": "Waiting for message", "ndv.output.outType.logs": "Logs", "ndv.output.outType.regular": "Output", "ndv.output.edit": "Edit Output", "ndv.output.all": "all", "ndv.output.branch": "Branch", "ndv.output.executing": "Executing node...", "ndv.output.items": "{count} item | {count} items", "ndv.output.andSubExecutions": ", {count} sub-execution | , {count} sub-executions", "ndv.output.noOutputData.message": "n8n stops executing the workflow when a node has no output data. You can change this default behaviour via", "ndv.output.noOutputData.message.settings": "Settings", "ndv.output.noOutputData.message.settingsOption": "> \"Always Output Data\".", "ndv.output.noOutputData.title": "No output data returned", "ndv.output.noOutputData.v2.title": "No output data", "ndv.output.noOutputData.v2.description": "{link} to view output data", "ndv.output.noOutputData.v2.action": "Test this step", "ndv.output.noOutputData.trigger.title": "No trigger output", "ndv.output.noOutputData.trigger.action": "Test this trigger", "ndv.output.noOutputDataInBranch": "No output data in this branch", "ndv.output.of": "{current} of {total}", "ndv.output.pageSize": "<PERSON>", "ndv.output.run": "Run", "ndv.output.runNodeHint": "Execute this node to view data", "ndv.output.runNodeHintSubNode": "Output will appear here once the parent node is run", "ndv.output.githubNodeWaitingForWebhook": "Execution will continue when the following webhook URL is called: ", "ndv.output.sendAndWaitWaitingApproval": "Execution will continue after the user's response", "ndv.output.waitNodeWaiting.title": "Waiting for input", "ndv.output.waitNodeWaiting.description.webhook": "Execution will continue when webhook is received on ", "ndv.output.waitNodeWaiting.description.form": "Execution will continue when form is submitted on ", "ndv.output.waitNodeWaiting.description.timer": "Execution will continue when wait time is over", "ndv.output.insertTestData": "set mock data", "ndv.output.staleDataWarning.regular": "Node parameters have changed.<br>Test node again to refresh output.", "ndv.output.staleDataWarning.pinData": "Node parameter changes will not affect pinned output data.", "ndv.output.tooMuchData.message": "The node contains {size} MB of data. Displaying it may slow down your browser temporarily.", "ndv.output.tooMuchData.showDataAnyway": "Show data", "ndv.output.tooMuchData.title": "Display data?", "ndv.output.waitingToRun": "Waiting to execute...", "ndv.output.noToolUsedInfo": "None of your tools were used in this run. Try giving your tools clearer names and descriptions to help the AI", "ndv.title.cancel": "Cancel", "ndv.title.rename": "<PERSON><PERSON>", "ndv.title.rename.placeholder": "Enter new name...", "ndv.title.renameNode": "Rename node", "ndv.pinData.pin.title": "Pin data", "ndv.pinData.pin.description": "Node will always output current data instead of executing. Doesn't apply to production executions.", "ndv.pinData.pin.binary": "Pin Data is disabled as this node's output contains binary data.", "ndv.pinData.pin.link": "More info", "ndv.pinData.unpin.title": "Unpin data", "ndv.pinData.pin.multipleRuns.title": "Run #{index} was pinned", "ndv.pinData.pin.multipleRuns.description": "This run will be outputted each time the node is run.", "ndv.pinData.unpinAndExecute.title": "Unpin output data?", "ndv.pinData.unpinAndExecute.description": "Testing a node overwrites pinned data.", "ndv.pinData.unpinAndExecute.cancel": "Cancel", "ndv.pinData.unpinAndExecute.confirm": "Unpin and test", "ndv.pinData.beforeClosing.title": "Save output changes before closing?", "ndv.pinData.beforeClosing.cancel": "Discard", "ndv.pinData.beforeClosing.confirm": "Save", "ndv.pinData.error.syntaxError.title": "Unable to save due to invalid JSON", "ndv.pinData.error.tooLarge.title": "Unable to pin data due to size limit", "ndv.pinData.error.tooLarge.description": "Workflow has reached the maximum allowed pinned data size ({size} mb / {limit} mb)", "ndv.pinData.error.tooLargeWorkflow.title": "Unable to pin data due to size limit", "ndv.pinData.error.tooLargeWorkflow.description": "Workflow has reached the maximum allowed size ({size} mb / {limit} mb)", "ndv.httpRequest.credentialOnly.docsNotice": "Use the <a target=\"_blank\" href=\"{docsUrl}\">{nodeName} docs</a> to construct your request. We'll take care of the authentication part if you add a {nodeName} credential below.", "noTagsView.readyToOrganizeYourWorkflows": "Ready to organize your workflows?", "noTagsView.withWorkflowTagsYouReFree": "With workflow tags, you're free to create the perfect tagging system for your flows", "noAnnotationTagsView.title": "Organize your executions", "noAnnotationTagsView.description": "Execution tags help you label and identify different classes of execution. Plus once you tag an execution, it’s never deleted", "node.thisIsATriggerNode": "This is a Trigger node. <a target=\"_blank\" href=\"https://docs.n8n.io/workflows/components/nodes/\">Learn more</a>", "node.activateDeactivateNode": "Activate/Deactivate Node", "node.changeColor": "Change color", "node.disabled": "Deactivated", "node.testStep": "Execute step", "node.disable": "Deactivate", "node.enable": "Activate", "node.delete": "Delete", "node.add": "Add", "node.issues": "Issues", "node.dirty": "Node configuration changed. Output data may change when this node is run again", "node.subjectToChange": "Because of changes in the workflow, output data may change when this node is run again", "node.nodeIsExecuting": "<PERSON>de is executing", "node.nodeIsWaitingTill": "Node is waiting until {date} {time}", "node.theNodeIsWaitingIndefinitelyForAnIncomingWebhookCall": "The node is waiting for an incoming webhook call (indefinitely)", "node.theNodeIsWaitingWebhookCall": "The node is waiting for an incoming webhook call", "node.theNodeIsWaitingFormCall": "The node is waiting for a form submission", "node.theNodeIsWaitingUserInput": "The node is waiting for user input", "node.waitingForYouToCreateAnEventIn": "Waiting for you to create an event in {nodeType}", "node.discovery.pinData.canvas": "You can pin this output instead of waiting for a test event. Open node to do so.", "node.discovery.pinData.ndv": "You can pin this output instead of waiting for a test event.", "node.executionError.openNode": "Open node", "nodeBase.clickToAddNodeOrDragToConnect": "Click to add node \n or drag to connect", "nodeCreator.actionsPlaceholderNode.scheduleTrigger": "On a Schedule", "nodeCreator.actionsPlaceholderNode.webhook": "On a Webhook call", "nodeCreator.actionsCategory.actions": "Actions", "nodeCreator.actionsCategory.onNewEvent": "On new {event} event", "nodeCreator.actionsCategory.onEvent": "On {event}", "nodeCreator.actionsCategory.triggers": "Triggers", "nodeCreator.actionsCategory.triggerNodes": "<PERSON><PERSON>", "nodeCreator.actionsCategory.regularNodes": "Regular Nodes", "nodeCreator.actionsCategory.regularAndTriggers": "Regular & Trigger Nodes", "nodeCreator.actionsCategory.searchActions": "Search {node} Actions...", "nodeCreator.actionsCategory.noMatchingActions": "No matching Actions. <i>Reset search</i>", "nodeCreator.actionsCategory.noMatchingTriggers": "No matching Triggers. <i>Reset search</i>", "nodeCreator.actionsList.apiCall": "Didn't find the right event? Make a <a data-action='addHttpNode'>custom {node} API call</a>", "nodeCreator.actionsCallout.noActionItems": "We don't have <strong>{nodeName}</strong> actions yet. Have one in mind? Make a <a target=\"_blank\" href=\"https://community.n8n.io/c/feature-requests/5\"> request in our community</a>", "nodeCreator.actionsCallout.triggersStartWorkflow": "Actions need to be triggered by another node, e.g. at regular intervals with the <strong>Schedule</strong> node. <a target=\"_blank\" href=\"https://docs.n8n.io/integrations/builtin/\"> Learn more</a>", "nodeCreator.actionsTooltip.triggersStartWorkflow": "A trigger is a step that starts your workflow. <a target=\"_blank\" href=\"https://docs.n8n.io/integrations/builtin/\"> Learn more</a>", "nodeCreator.actionsTooltip.actionsPerformStep": "Actions perform a step once your workflow has already started. <a target=\"_blank\" href=\"https://docs.n8n.io/integrations/builtin/\"> Learn more</a>", "nodeCreator.actionsCallout.noTriggerItems": "No <strong>{nodeName}</strong> Triggers available. Users often combine the following Triggers with <strong>{nodeName}</strong> Actions.", "nodeCreator.categoryNames.otherCategories": "Results in other categories", "nodeCreator.categoryNames.moreFromCommunity": "More from the community", "nodeCreator.subnodes": "sub-nodes", "nodeCreator.noResults.dontWorryYouCanProbablyDoItWithThe": "Don’t worry, you can probably do it with the", "nodeCreator.noResults.httpRequest": "HTTP Request", "nodeCreator.noResults.node": "node", "nodeCreator.noResults.or": "or", "nodeCreator.noResults.requestTheNode": "Request the node", "nodeCreator.noResults.wantUsToMakeItFaster": "Want us to make it faster?", "nodeCreator.noResults.weDidntMakeThatYet": "We didn't make that... yet", "nodeCreator.noResults.webhook": "Webhook", "nodeCreator.ragStarterTemplate.openTemplateItem.title": "RAG starter template", "nodeCreator.ragStarterTemplate.openTemplateItem.description": "Get a feel for vector stores in n8n", "nodeCreator.searchBar.searchNodes": "Search nodes...", "nodeCreator.subcategoryDescriptions.appTriggerNodes": "Runs the flow when something happens in an app like Telegram, Notion or Airtable", "nodeCreator.subcategoryDescriptions.appRegularNodes": "Do something in an app or service like Google Sheets, Telegram or Notion", "nodeCreator.subcategoryDescriptions.dataTransformation": "Manipulate, filter or convert data", "nodeCreator.subcategoryDescriptions.files": "CSV, XLS, XML, text, images, etc.", "nodeCreator.subcategoryDescriptions.flow": "Branch, merge or loop the flow, etc.", "nodeCreator.subcategoryDescriptions.helpers": "Run code, make HTTP requests, set webhooks, etc.", "nodeCreator.subcategoryDescriptions.otherTriggerNodes": "Runs the flow on workflow errors, file changes, etc.", "nodeCreator.subcategoryDescriptions.agents": "Autonomous entities that interact and make decisions.", "nodeCreator.subcategoryDescriptions.chains": "Structured assemblies for specific tasks.", "nodeCreator.subcategoryDescriptions.documentLoaders": "Handles loading of documents for processing.", "nodeCreator.subcategoryDescriptions.embeddings": "Transforms text into vector representations.", "nodeCreator.subcategoryDescriptions.languageModels": "AI models that understand and generate language.", "nodeCreator.subcategoryDescriptions.memory": "Manages storage and retrieval of information during execution.", "nodeCreator.subcategoryDescriptions.outputParsers": "Ensures the output adheres to a defined format.", "nodeCreator.subcategoryDescriptions.retrievers": "Fetches relevant information from a source.", "nodeCreator.subcategoryDescriptions.textSplitters": "Breaks down text into smaller parts.", "nodeCreator.subcategoryDescriptions.tools": "Utility components providing various functionalities.", "nodeCreator.subcategoryDescriptions.vectorStores": "Handles storage and retrieval of vector representations.", "nodeCreator.subcategoryDescriptions.miscellaneous": "Other AI related nodes.", "nodeCreator.subcategoryDescriptions.humanInTheLoop": "Wait for approval or human input before continuing", "nodeCreator.subcategoryInfos.languageModels": "Chat models are designed for interactive conversations and follow instructions well, while text completion models focus on generating continuations of a given text input", "nodeCreator.subcategoryInfos.memory": "Memory allows an AI model to remember and reference past interactions with it", "nodeCreator.subcategoryInfos.vectorStores": "Vector stores allow an AI model to reference relevant pieces of documents, useful for question answering and document search", "nodeCreator.subcategoryNames.appTriggerNodes": "On app event", "nodeCreator.subcategoryNames.appRegularNodes": "Action in an app", "nodeCreator.subcategoryNames.dataTransformation": "Data transformation", "nodeCreator.subcategoryNames.files": "Files", "nodeCreator.subcategoryNames.flow": "Flow", "nodeCreator.subcategoryNames.helpers": "Core", "nodeCreator.subcategoryNames.otherTriggerNodes": "Other ways...", "nodeCreator.subcategoryNames.agents": "Agents", "nodeCreator.subcategoryNames.chains": "Chains", "nodeCreator.subcategoryNames.documentLoaders": "Document Loaders", "nodeCreator.subcategoryNames.embeddings": "Embeddings", "nodeCreator.subcategoryNames.languageModels": "Language Models", "nodeCreator.subcategoryNames.memory": "Memory", "nodeCreator.subcategoryNames.outputParsers": "Output Parsers", "nodeCreator.subcategoryNames.retrievers": "Retrievers", "nodeCreator.subcategoryNames.textSplitters": "Text Splitters", "nodeCreator.subcategoryNames.tools": "Tools", "nodeCreator.subcategoryNames.vectorStores": "Vector Stores", "nodeCreator.subcategoryNames.miscellaneous": "Miscellaneous", "nodeCreator.subcategoryNames.humanInTheLoop": "Human in the loop", "nodeCreator.sectionNames.popular": "Popular", "nodeCreator.sectionNames.other": "Other", "nodeCreator.sectionNames.sendAndWait": "Send and wait for response", "nodeCreator.sectionNames.transform.combine": "Combine items", "nodeCreator.sectionNames.transform.addOrRemove": "Add or remove items", "nodeCreator.sectionNames.transform.convert": "Convert data", "nodeCreator.triggerHelperPanel.addAnotherTrigger": "Add another trigger", "nodeCreator.triggerHelperPanel.addAnotherTriggerDescription": "Triggers start your workflow. Workflows can have multiple triggers.", "nodeCreator.triggerHelperPanel.title": "When should this workflow run?", "nodeCreator.triggerHelperPanel.scheduleTriggerDisplayName": "On a schedule", "nodeCreator.triggerHelperPanel.scheduleTriggerDescription": "Runs the flow every day, hour, or custom interval", "nodeCreator.triggerHelperPanel.webhookTriggerDisplayName": "On webhook call", "nodeCreator.triggerHelperPanel.webhookTriggerDescription": "Runs the flow on receiving an HTTP request", "nodeCreator.triggerHelperPanel.formTriggerDisplayName": "On form submission", "nodeCreator.triggerHelperPanel.formTriggerDescription": "Generate webforms in n8n and pass their responses to the workflow", "nodeCreator.triggerHelperPanel.formDisplayName": "Form", "nodeCreator.triggerHelperPanel.formDescription": "Add next form page", "nodeCreator.triggerHelperPanel.manualTriggerDisplayName": "Trigger manually", "nodeCreator.triggerHelperPanel.manualTriggerDescription": "Runs the flow on clicking a button in n8n. Good for getting started quickly", "nodeCreator.triggerHelperPanel.manualChatTriggerDisplayName": "On chat message", "nodeCreator.triggerHelperPanel.manualChatTriggerDescription": "Runs the flow when a user sends a chat message. For use with AI nodes", "nodeCreator.triggerHelperPanel.manualTriggerTag": "Recommended", "nodeCreator.triggerHelperPanel.chatTriggerDisplayName": "On chat message", "nodeCreator.triggerHelperPanel.chatTriggerDescription": "Runs the flow when a user sends a chat message. For use with AI nodes", "nodeCreator.triggerHelperPanel.whatHappensNext": "What happens next?", "nodeCreator.triggerHelperPanel.selectATrigger": "What triggers this workflow?", "nodeCreator.triggerHelperPanel.selectATriggerDescription": "A trigger is a step that starts your workflow", "nodeCreator.triggerHelperPanel.workflowTriggerDisplayName": "When executed by another workflow", "nodeCreator.triggerHelperPanel.workflowTriggerDescription": "Runs the flow when called by the Execute Workflow node from a different workflow", "nodeCreator.aiPanel.aiNodes": "AI Nodes", "nodeCreator.aiPanel.aiOtherNodes": "Other AI Nodes", "nodeCreator.aiPanel.aiOtherNodesDescription": "Embeddings, Vector Stores, LLMs and other AI nodes", "nodeCreator.aiPanel.selectAiNode": "Select an AI Node to add to your workflow", "nodeCreator.aiPanel.nodesForAi": "Build autonomous agents, summarize or search documents, etc.", "nodeCreator.aiPanel.newTag": "New", "nodeCreator.aiPanel.langchainAiNodes": "AI", "nodeCreator.aiPanel.title": "When should this workflow run?", "nodeCreator.aiPanel.linkItem.description": "See what's possible and get started 5x faster", "nodeCreator.aiPanel.linkItem.title": "AI Templates", "nodeCreator.aiPanel.scheduleTriggerDisplayName": "On a schedule", "nodeCreator.aiPanel.scheduleTriggerDescription": "Runs the flow every day, hour, or custom interval", "nodeCreator.aiPanel.webhookTriggerDisplayName": "On webhook call", "nodeCreator.aiPanel.webhookTriggerDescription": "Runs the flow when another app sends a webhook", "nodeCreator.aiPanel.manualTriggerDisplayName": "Manually", "nodeCreator.aiPanel.manualTriggerDescription": "Runs the flow on clicking a button in n8n", "nodeCreator.aiPanel.whatHappensNext": "What happens next?", "nodeCreator.aiPanel.selectATrigger": "Select an AI Component", "nodeCreator.aiPanel.selectATriggerDescription": "A trigger is a step that starts your workflow", "nodeCreator.aiPanel.workflowTriggerDisplayName": "When Executed by Another Workflow", "nodeCreator.aiPanel.workflowTriggerDescription": "Runs the flow when called by the Execute Workflow node from a different workflow", "nodeCreator.nodeItem.triggerIconTitle": "<PERSON><PERSON>", "nodeCreator.nodeItem.aiIconTitle": "LangChain AI Node", "nodeCreator.nodeItem.deprecated": "Deprecated", "nodeCredentials.createNew": "Create new credential", "nodeCredentials.credentialFor": "Credential for {credentialType}", "nodeCredentials.credentialsLabel": "Credential to connect with", "nodeCredentials.issues": "Issues", "nodeCredentials.selectCredential": "Select Credential", "nodeCredentials.selectedCredentialUnavailable": "{name} (unavailable)", "nodeCredentials.showMessage.message": "Nodes that used credential \"{oldCredentialName}\" have been updated to use \"{newCredentialName}\"", "nodeCredentials.showMessage.title": "Node credential updated", "nodeCredentials.updateCredential": "Update Credential", "nodeErrorView.cause": "Cause", "nodeErrorView.copyToClipboard": "Copy to Clipboard", "nodeErrorView.copyToClipboard.tooltip": "Copy error details for debugging. Copied data may contain sensitive information. Proceed with caution when sharing.", "nodeErrorView.dataBelowMayContain": "Data below may contain sensitive information. Proceed with caution when sharing.", "nodeErrorView.details": "Details", "nodeErrorView.details.from": "From {node}", "nodeErrorView.details.rawMessages": "Full message", "nodeErrorView.details.errorData": "Error data", "nodeErrorView.details.errorExtra": "Error extra", "nodeErrorView.details.request": "Request", "nodeErrorView.details.title": "Error details", "nodeErrorView.details.message": "Error message", "nodeErrorView.details.info": "Other info", "nodeErrorView.details.nodeVersion": "Node version", "nodeErrorView.details.nodeType": "Node type", "nodeErrorView.details.n8nVersion": "n8n version", "nodeErrorView.details.errorCause": "Error cause", "nodeErrorView.details.causeDetailed": "Cause detailed", "nodeErrorView.details.stackTrace": "Stack trace", "nodeErrorView.error": "ERROR", "nodeErrorView.errorSubNode": "Error in sub-node ‘{node}’", "nodeErrorView.httpCode": "HTTP Code", "nodeErrorView.errorCode": "Error code", "nodeErrorView.inParameter": "In or underneath Parameter", "nodeErrorView.itemIndex": "Item Index", "nodeErrorView.runIndex": "Run Index", "nodeErrorView.showMessage.title": "Copied to clipboard", "nodeErrorView.stack": "<PERSON><PERSON>", "nodeErrorView.theErrorCauseIsTooLargeToBeDisplayed": "The error cause is too large to be displayed", "nodeErrorView.time": "Time", "nodeErrorView.inputPanel.previousNodeError.title": "Error running node '{nodeName}'", "nodeErrorView.description.pairedItemInvalidInfo": "An expression here won't work because it uses <code>.item</code> and n8n can't figure out the <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/\">matching item</a>. This is because the node <strong>'{nodeCause}'</strong> returned incorrect matching information (for item {itemIndex} of run {runIndex}).  <br/><br/>Try using <code>.first()</code>, <code>.last()</code> or <code>.all()[index]</code> instead of <code>.item</code>.", "nodeErrorView.description.pairedItemNoInfo": "An expression here won't work because it uses <code>.item</code> and n8n can't figure out the <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/\">matching item</a>. The node <strong>'{nodeCause}'</strong> didn't return enough information.", "nodeErrorView.description.pairedItemNoInfoCodeNode": "An expression here won't work because it uses <code>.item</code> and n8n can't figure out the <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/\">matching item</a>. You can either: <ul><li>Add the <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/\">missing information</a> to the node <strong>'{nodeCause}'</strong></li><li>Or use <code>.first()</code>, <code>.last()</code> or <code>.all()[index]</code> instead of <code>.item</code></li></ul>", "nodeErrorView.description.pairedItemNoConnection": "There is no connection back to the node <strong>'{nodeCause}'</strong>, but it's used in an expression here.<br/><br/>Please wire up the node (there can be other nodes in between).", "nodeErrorView.description.pairedItemNoConnectionCodeNode": "There is no connection back to the node <strong>'{nodeCause}'</strong>, but it's used in code here.<br/><br/>Please wire up the node (there can be other nodes in between).", "nodeErrorView.description.noNodeExecutionData": "An expression references the node <strong>'{nodeCause}'</strong>, but it hasn't been executed yet. Either change the expression, or re-wire your workflow to make sure that node executes first.", "nodeErrorView.description.nodeNotFound": "The node <strong>'{nodeCause}'</strong> doesn't exist, but it's used in an expression here.", "nodeErrorView.description.noInputConnection": "This node has no input data. Please make sure this node is connected to another node.", "nodeErrorView.description.pairedItemMultipleMatches": "An expression here won't work because it uses <code>.item</code> and n8n can't figure out the <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/\">matching item</a>. (There are multiple possible matches) <br/><br/>Try using <code>.first()</code>, <code>.last()</code> or <code>.all()[index]</code> instead of <code>.item</code> or <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/\">reference a different node</a>.", "nodeErrorView.description.pairedItemMultipleMatchesCodeNode": "The code here won't work because it uses <code>.item</code> and n8n can't figure out the <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/\">matching item</a>. (There are multiple possible matches) <br/><br/>Try using <code>.first()</code>, <code>.last()</code> or <code>.all()[index]</code> instead of <code>.item</code> or <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-code-node/\">reference a different node</a>.", "nodeErrorView.description.pairedItemPinned": "The <a target=\"_blank\" href=\"https://docs.n8n.io/data/data-mapping/data-item-linking/item-linking-errors/\">item-matching</a> data in that node may be stale. It is needed by an expression in this node that uses <code>.item</code>.", "nodeHelpers.credentialsUnset": "Credentials for '{credentialType}' are not set.", "nodeSettings.alwaysOutputData.description": "If active, will output a single, empty item when the output would have been empty. Use to prevent the workflow finishing on this node.", "nodeSettings.alwaysOutputData.displayName": "Always Output Data", "nodeSettings.clickOnTheQuestionMarkIcon": "Click the '?' icon to open this node on n8n.io", "nodeSettings.onError.description": "Action to take when the node execution fails", "nodeSettings.onError.displayName": "On Error", "nodeSettings.onError.options.continueRegularOutput.description": "Pass error message as item in regular output", "nodeSettings.onError.options.continueRegularOutput.displayName": "Continue", "nodeSettings.onError.options.continueErrorOutput.description": "Pass item to an extra `error` output", "nodeSettings.onError.options.continueErrorOutput.displayName": "Continue (using error output)", "nodeSettings.onError.options.stopWorkflow.description": "Halt execution and fail workflow", "nodeSettings.onError.options.stopWorkflow.displayName": "Stop Workflow", "nodeSettings.docs": "Docs", "nodeSettings.executeButtonTooltip.times": "Will execute {inputSize} times, once for each input item", "nodeSettings.executeOnce.description": "If active, the node executes only once, with data from the first item it receives", "nodeSettings.executeOnce.displayName": "Execute Once", "nodeSettings.maxTries.description": "Number of times to attempt to execute the node before failing the execution", "nodeSettings.maxTries.displayName": "<PERSON><PERSON>", "nodeSettings.noDescriptionFound": "No description found", "nodeSettings.nodeDescription": "Node Description", "nodeSettings.notes.description": "Optional note to save with the node", "nodeSettings.notes.displayName": "Notes", "nodeSettings.notesInFlow.description": "If active, the note above will display in the flow as a subtitle", "nodeSettings.notesInFlow.displayName": "Display Note in Flow?", "nodeSettings.parameters": "Parameters", "nodeSettings.settings": "Settings", "nodeSettings.communityNodeTooltip": "This is a <a href=\"{docUrl}\" target=\"_blank\"/>community node</a>", "nodeSettings.retryOnFail.description": "If active, the node tries to execute again when it fails", "nodeSettings.retryOnFail.displayName": "Retry On Fail", "nodeSettings.scopes.expandedNoticeWithScopes": "<a data-key=\"toggle-expand\">{count} scope</a> available for {activeCredential} credentials<br>{scopes}<br><a data-key=\"show-less\">Show less</a> | <a data-key=\"toggle-expand\">{count} scopes</a> available for {activeCredential} credentials<br>{scopes}<br><a data-key=\"show-less\">Show less</a>", "nodeSettings.scopes.notice": "<a data-key=\"toggle-expand\">{count} scope</a> available for {activeCredential} credentials | <a data-key=\"toggle-expand\">{count} scopes</a> available for {activeCredential} credentials", "nodeSettings.theNodeIsNotValidAsItsTypeIsUnknown": "The node is not valid as its type ({nodeType}) is unknown", "nodeSettings.communityNodeDetails.title": "Node details", "nodeSettings.communityNodeUnknown.title": "Install this node to use it", "nodeSettings.communityNodeUnknown.description": "This node is not currently installed. It's part of the {action} community package.", "nodeSettings.communityNodeUnknown.installLink.text": "How to install community nodes", "nodeSettings.nodeTypeUnknown.description": "This node is not currently installed. It is either from a newer version of n8n, a {action}, or has an invalid structure", "nodeSettings.nodeTypeUnknown.description.customNode": "custom node", "nodeSettings.thisNodeDoesNotHaveAnyParameters": "This node does not have any parameters", "nodeSettings.useTheHttpRequestNode": "Use the <b>HTTP Request</b> node to make a custom API call. We'll take care of the {nodeTypeDisplayName} auth for you. <a target=\"_blank\" href=\"https://docs.n8n.io/integrations/custom-operations/\">Learn more</a>", "nodeSettings.waitBetweenTries.description": "How long to wait between each attempt (in milliseconds)", "nodeSettings.waitBetweenTries.displayName": "Wait Between Tries (ms)", "nodeSettings.hasForeignCredential": "To edit this node, either:<br/>a) Ask {owner} to share the credential with you, or<br/>b) Duplicate the node and add your own credential", "nodeSettings.latest": "Latest", "nodeSettings.deprecated": "Deprecated", "nodeSettings.latestVersion": "Latest version: {version}", "nodeSettings.outputCleared.title": "Parameters changed", "nodeSettings.outputCleared.message": "Order of parameters changed, outgoing connections were cleared", "nodeSettings.nodeVersion": "{node} node version {version}", "nodeView.addNode": "Add node", "nodeView.openFocusPanel": "Open focus panel", "nodeView.openNodesPanel": "Open nodes panel", "nodeView.addATriggerNodeFirst": "Add a <a data-action='showNodeCreator'>Trigger <PERSON>de</a> first", "nodeView.addOrEnableTriggerNode": "<a data-action='showNodeCreator'>Add</a> or enable a Trigger node to execute the workflow", "nodeView.addSticky": "Click to add sticky note", "nodeView.addStickyHint": "Add sticky note", "nodeView.cantExecuteNoTrigger": "Cannot execute workflow", "nodeView.canvasAddButton.addATriggerNodeBeforeExecuting": "Add a Trigger Node before executing the workflow", "nodeView.canvasAddButton.addFirstStep": "Add first step…", "nodeView.templateLink": "or start from a template", "nodeView.confirmMessage.onClipboardPasteEvent.cancelButtonText": "", "nodeView.confirmMessage.onClipboardPasteEvent.confirmButtonText": "Yes, import", "nodeView.confirmMessage.onClipboardPasteEvent.headline": "Import Workflow?", "nodeView.confirmMessage.onClipboardPasteEvent.message": "Workflow will be imported from<br /><i>{plainTextData}<i>", "nodeView.confirmMessage.debug.cancelButtonText": "Cancel", "nodeView.confirmMessage.debug.confirmButtonText": "Unpin", "nodeView.confirmMessage.debug.headline": "Unpin workflow data", "nodeView.confirmMessage.debug.message": "Loading this execution will unpin the data currently pinned in these nodes", "nodeView.couldntImportWorkflow": "Could not import workflow", "nodeView.couldntLoadWorkflow.invalidWorkflowObject": "Invalid workflow object", "nodeView.deletesTheCurrentExecutionData": "Deletes the current execution data", "nodeView.focusPanel.title": "Focus", "nodeView.focusPanel.noParameters": "No parameters focused. Focus a parameter by clicking on the action dropdown in the node detail view.", "nodeView.focusPanel.missingParameter": "This parameter is no longer visible on the node. A related parameter was likely changed, removing this one.", "nodeView.itLooksLikeYouHaveBeenEditingSomething": "It looks like you made some edits. If you leave before saving, your changes will be lost.", "nodeView.loadingTemplate": "Loading template", "nodeView.moreInfo": "More info", "nodeView.noNodesGivenToAdd": "No nodes to add specified", "nodeView.prompt.cancel": "@:_reusableBaseText.cancel", "nodeView.prompt.invalidName": "Invalid Name", "nodeView.prompt.newName": "New Name", "nodeView.prompt.rename": "<PERSON><PERSON>", "nodeView.prompt.renameNode": "<PERSON><PERSON>", "nodeView.redirecting": "Redirecting", "nodeView.refresh": "Refresh", "nodeView.resetZoom": "Reset Zoom", "nodeView.tidyUp": "Tidy Up", "nodeView.runButtonText.executeWorkflow": "Execute workflow", "nodeView.runButtonText.executingWorkflow": "Executing workflow", "nodeView.runButtonText.waitingForTriggerEvent": "Waiting for trigger event", "nodeView.runButtonText.from": "from {nodeName}", "nodeView.showError.workflowError": "Workflow execution had an error", "nodeView.showError.getWorkflowDataFromUrl.title": "Problem loading workflow", "nodeView.showError.importWorkflowData.title": "Problem importing workflow", "nodeView.showError.mounted1.message": "There was a problem loading init data", "nodeView.showError.mounted1.title": "Init Problem", "nodeView.showError.mounted2.message": "There was a problem initializing the workflow", "nodeView.showError.mounted2.title": "Init Problem", "nodeView.showError.openExecution.title": "Problem loading execution", "nodeView.showError.openExecution.node": "Problem opening node in execution", "nodeView.showError.openWorkflow.title": "Problem opening workflow", "nodeView.showError.stopExecution.title": "Problem stopping execution", "nodeView.showError.stopWaitingForWebhook.title": "Problem deleting test webhook", "nodeView.showError.nodeNodeCompatible.title": "Connection not possible", "nodeView.showError.nodeNodeCompatible.message": "The node \"{sourceNodeName}\" can't be connected to the node \"{targetNodeName}\" because they are not compatible.", "nodeView.showMessage.addNodeButton.message": "'{nodeType<PERSON><PERSON>}' is an unknown node type", "nodeView.showMessage.addNodeButton.title": "Could not insert node", "nodeView.showMessage.keyDown.title": "Workflow created", "nodeView.showMessage.showMaxNodeTypeError.message": "Only one '{nodeTypeDataDisplayName}' node is allowed in a workflow | Only {count} '{nodeTypeDataDisplayName}' nodes are allowed in a workflow", "nodeView.showMessage.showMaxNodeTypeError.title": "Could not insert node", "nodeView.showMessage.stopExecutionCatch.unsaved.message": "This execution was canceled", "nodeView.showMessage.stopExecutionCatch.unsaved.title": "Execution canceled", "nodeView.showMessage.stopExecutionCatch.message": "It completed before it could be stopped", "nodeView.showMessage.stopExecutionCatch.title": "Workflow finished executing", "nodeView.showMessage.stopExecutionTry.title": "Execution stopped", "nodeView.showMessage.debug.title": "Execution data imported", "nodeView.showMessage.debug.content": "You can make edits and re-execute. Once you're done, unpin the the first node.", "nodeView.showMessage.debug.missingNodes.title": "Some execution data wasn't imported", "nodeView.showMessage.debug.missingNodes.content": "Some nodes have been deleted or renamed or added to the workflow since the execution ran.", "nodeView.showMessage.ndvUrl.missingNodes.title": "Node not found", "nodeView.showMessage.ndvUrl.missingNodes.content": "URL contained a reference to an unknown node. Maybe the node was deleted?", "nodeView.stopCurrentExecution": "Stop current execution", "nodeView.stopWaitingForWebhookCall": "Stop waiting for webhook call", "nodeView.stoppingCurrentExecution": "Stopping current execution", "nodeView.thereWasAProblemLoadingTheNodeParametersOfNode": "There was a problem loading the parameters of the node", "nodeView.thisExecutionHasntFinishedYet": "This execution hasn't finished yet", "nodeView.toSeeTheLatestStatus": "to see the latest status", "nodeView.workflowTemplateWithIdCouldNotBeFound": "Workflow template with ID \"{templateId}\" could not be found", "nodeView.workflowWithIdCouldNotBeFound": "Workflow with ID \"{workflowId}\" could not be found", "nodeView.zoomIn": "Zoom In", "nodeView.zoomOut": "Zoom Out", "nodeView.zoomToFit": "Zoom to Fit", "nodeView.replaceMe": "Replace Me", "nodeView.setupTemplate": "Set up template", "nodeView.expandAllNodes": "Expand all nodes", "nodeView.collapseAllNodes": "Collapse all nodes", "nodeViewV2.showError.editingNotAllowed": "Editing is not allowed", "nodeViewV2.showError.failedToCreateNode": "Failed to create node", "contextMenu.node": "node | nodes", "contextMenu.sticky": "sticky note | sticky notes", "contextMenu.selectAll": "Select all", "contextMenu.deselectAll": "Clear selection", "contextMenu.tidyUpWorkflow": "Tidy up workflow", "contextMenu.tidyUpSelection": "Tidy up selection", "contextMenu.extract": "Convert node to sub-workflow | Convert {count} nodes to sub-workflow", "contextMenu.duplicate": "Duplicate | Duplicate {count} {subject}", "contextMenu.open": "Open...", "contextMenu.test": "Execute step", "contextMenu.rename": "<PERSON><PERSON>", "contextMenu.openSubworkflow": "Go to Sub-workflow", "contextMenu.copy": "Copy | Copy {count} {subject}", "contextMenu.deactivate": "Deactivate | Deactivate {count} {subject}", "contextMenu.activate": "Activate | Activate {count} nodes", "contextMenu.pin": "Pin | Pin {count} nodes", "contextMenu.unpin": "Unpin | Unpin {count} nodes", "contextMenu.delete": "Delete | Delete {count} {subject}", "contextMenu.addNode": "Add node", "contextMenu.addSticky": "Add sticky note", "contextMenu.editSticky": "Edit sticky note", "contextMenu.changeColor": "Change color", "nodeWebhooks.clickToCopyWebhookUrls": "Click to copy webhook URLs", "nodeWebhooks.clickToCopyWebhookUrls.formTrigger": "Click to copy Form URL", "nodeWebhooks.clickToCopyWebhookUrls.chatTrigger": "Click to copy Chat URL", "nodeWebhooks.clickToCopyWebhookUrls.mcpTrigger": "Click to copy MCP URL", "nodeWebhooks.clickToDisplayWebhookUrls": "Click to display webhook URLs", "nodeWebhooks.clickToDisplayWebhookUrls.formTrigger": "Click to display Form URL", "nodeWebhooks.clickToDisplayWebhookUrls.chatTrigger": "Click to display Chat URL", "nodeWebhooks.clickToDisplayWebhookUrls.mcpTrigger": "Click to display MCP URL", "nodeWebhooks.clickToHideWebhookUrls": "Click to hide webhook URLs", "nodeWebhooks.clickToHideWebhookUrls.formTrigger": "Click to hide Form URL", "nodeWebhooks.clickToHideWebhookUrls.chatTrigger": "Click to hide Chat URL", "nodeWebhooks.clickToHideWebhookUrls.mcpTrigger": "Click to hide MCP URL", "nodeWebhooks.invalidExpression": "[INVALID EXPRESSION]", "nodeWebhooks.productionUrl": "Production URL", "nodeWebhooks.showMessage.title": "URL copied", "nodeWebhooks.showMessage.title.formTrigger": "Form URL copied", "nodeWebhooks.showMessage.title.chatTrigger": "Chat URL copied", "nodeWebhooks.showMessage.title.mcpTrigger": "MCP URL copied", "nodeWebhooks.showMessage.message.formTrigger": "Form submissions made via this URL will trigger the workflow when it's activated", "nodeWebhooks.showMessage.message.chatTrigger": "Chat submissions made via this URL will trigger the workflow when it's activated", "nodeWebhooks.testUrl": "Test URL", "nodeWebhooks.webhookUrls": "Webhook URLs", "nodeWebhooks.webhookUrls.formTrigger": "Form URLs", "nodeWebhooks.webhookUrls.chatTrigger": "Chat URL", "nodeWebhooks.webhookUrls.mcpTrigger": "MCP URL", "openWorkflow.workflowImportError": "Could not import workflow", "openWorkflow.workflowNotFoundError": "Could not find workflow", "parameterInput.expressionResult": "e.g. {result}", "parameterInput.tip": "Tip", "parameterInput.dragTipBeforePill": "Drag an", "parameterInput.inputField": "input field", "parameterInput.dragTipAfterPill": "from the left to use it here.", "parameterInput.learnMore": "Learn more", "parameterInput.result": "Result", "parameterInput.item": "<PERSON><PERSON>", "parameterInput.hoverTableItemTip": "You can also do this by hovering over input/output items in the table view", "parameterInput.emptyString": "[empty]", "parameterInput.customApiCall": "Custom API Call", "parameterInput.error": "ERROR", "parameterInput.expression": "Expression", "parameterInput.fixed": "Fixed", "parameterInput.formatHtml": "Format HTML", "parameterInput.issues": "Issues", "parameterInput.loadingOptions": "Loading options...", "parameterInput.loadOptionsErrorService": "Error fetching options from {service}", "parameterInput.loadOptionsError": "Error fetching options", "parameterInput.loadOptionsCredentialsRequired": "Set up credential to see options", "parameterInput.openEditWindow": "Open Edit Window", "parameterInput.parameter": "Parameter: \"{shortPath}\"", "parameterInput.parameterHasExpression": "Parameter: \"{shortPath}\" has an expression", "parameterInput.parameterHasIssues": "Parameter: \"{shortPath}\" has issues", "parameterInput.parameterHasIssuesAndExpression": "Parameter: \"{shortPath}\" has issues and an expression", "parameterInput.refreshList": "Refresh List", "parameterInput.clearContents": "Clear Contents", "parameterInput.focusParameter": "Focus parameter", "parameterInput.resetValue": "Reset Value", "parameterInput.select": "Select", "parameterInput.selectDateAndTime": "Select date and time", "parameterInput.selectACredentialTypeFromTheDropdown": "Select a credential type from the dropdown", "parameterInput.theValueIsNotSupported": "The value \"{checkValue}\" is not supported!", "parameterInput.selectedWorkflowIsArchived": "The selected workflow is archived", "parameterInputExpanded.openDocs": "Open docs", "parameterInputExpanded.thisFieldIsRequired": "This field is required", "parameterInputList.delete": "Delete", "parameterInputList.deleteParameter": "Delete Parameter", "parameterInputList.parameterOptions": "Parameter Options", "parameterInputList.loadingFields": "Loading fields...", "parameterInputList.loadingError": "Error loading fields. Refresh you page and try again.", "parameterInputList.callout.dismiss.confirm.text": "Do you want to permanently hide this?", "parameterInputList.callout.dismiss.confirm.confirmButtonText": "Confirm", "parameterInputList.callout.dismiss.confirm.cancelButtonText": "Cancel", "parameterOverride.overridePanelText": "Defined automatically by the <b>model</b>", "parameterOverride.applyOverrideButtonTooltip": "Let the model define this parameter", "parameterOverride.descriptionTooltip": "Explain to the LLM how it should generate this value, a good, specific description would allow LLMs to produce expected results much more often", "personalizationModal.businessOwner": "Business Owner", "personalizationModal.continue": "Continue", "personalizationModal.cicd": "CI/CD", "personalizationModal.cloudInfrastructureOrchestration": "Cloud infrastructure orchestration", "personalizationModal.customerIntegrations": "Customer integrations", "personalizationModal.customerSupport": "Customer support", "personalizationModal.customizeN8n": "Customize n8n to you", "personalizationModal.dataScience": "Data Science", "personalizationModal.devops": "Devops", "personalizationModal.digitalAgencyOrConsultant": "Marketing agency / consultancy", "personalizationModal.eCommerce": "Ecommerce", "personalizationModal.education": "Education", "personalizationModal.engineering": "Engineering", "personalizationModal.engineeringOrDevops": "Engineering / Devops", "personalizationModal.errorWhileSubmittingResults": "Error while submitting results", "personalizationModal.financeOrAccounting": "Finance / Accounting", "personalizationModal.financeOrInsurance": "Finance / Insurance", "personalizationModal.getStarted": "Get started", "personalizationModal.government": "Government", "personalizationModal.healthcare": "Healthcare", "personalizationModal.howAreYourCodingSkills": "How are your coding skills?", "personalizationModal.howBigIsYourCompany": "How big is your company?", "personalizationModal.hr": "HR", "personalizationModal.imNotUsingN8nForWork": "I'm not using n8n for work", "personalizationModal.it": "IT", "personalizationModal.legal": "Legal", "personalizationModal.lessThan20People": "Less than 20 people", "personalizationModal.managedServiceProvider": "Managed service provider", "personalizationModal.manufacturing": "Manufacturing", "personalizationModal.marketing": "Marketing", "personalizationModal.media": "Media", "personalizationModal.notSureYet": "Not sure yet", "personalizationModal.operations": "Operations", "personalizationModal.other": "Other", "personalizationModal.otherPleaseSpecify": "Other (please specify)", "personalizationModal.specifyReportedSource": "Specify how you heard about n8n", "personalizationModal.people": "people", "personalizationModal.physicalRetailOrServices": "Physical retail or services", "personalizationModal.product": "Product (e.g. fast prototyping)", "personalizationModal.realEstateOrConstruction": "Real estate / Construction", "personalizationModal.saas": "Software as a service", "personalizationModal.salesAndMarketing": "Sales and Marketing", "personalizationModal.security": "Security", "personalizationModal.select": "Select...", "personalizationModal.howDidYouHearAboutN8n": "How did you hear about n8n?", "personalizationModal.friendWordOfMouth": "Friend / Word of mouth", "personalizationModal.podcast": "Podcast", "personalizationModal.event": "Event", "personalizationModal.myself": "Myself", "personalizationModal.myTeam": "My team", "personalizationModal.otherTeams": "Other teams", "personalizationModal.specifyAutomationBeneficiary": "Who will your automations mainly be for?", "personalizationModal.specifyYourRole": "Please specify your role", "personalizationModal.specifyYourAutomationGoal": "Please specify your automation goal", "personalizationModal.specifyYourCompanysIndustry": "Specify your company's industry", "personalizationModal.support": "Support", "personalizationModal.systemsIntegrator": "Systems integrator / Automation agency", "personalizationModal.telecoms": "Telecoms", "personalizationModal.thanks": "Thanks!", "personalizationModal.theseQuestionsHelpUs": "These questions help us tailor n8n to you", "personalizationModal.whichRoleBestDescribesYou": "Which role best describes you?", "personalizationModal.whatAreYouLookingToAutomate": "What are you looking to automate?", "personalizationModal.whatBestDescribesYourCompany": "What best describes your company?", "personalizationModal.whichIndustriesIsYourCompanyIn": "Which industries is your company in?", "personalizationModal.specifySalesMarketingGoal": "Which parts of Sales and Marketing?", "personalizationModal.leadGeneration": "Lead generation, enrichment, routing", "personalizationModal.customerCommunication": "Customer communication", "personalizationModal.customerActions": "Actions when lead changes status", "personalizationModal.yourEmailAddress": "Your email address", "personalizationModal.email": "Enter your email..", "personalizationModal.adCampaign": "Ad campaign management", "personalizationModal.reporting": "Reporting", "personalizationModal.ticketingSystemsIntegrations": "Ticketing systems integrations", "personalizationModal.dataSynching": "Data syncing", "personalizationModal.incidentResponse": "Incident response", "personalizationModal.monitoringAndAlerting": "Monitoring and alerting", "personalizationModal.specifyUsageMode": "Are you looking to do any of these?", "personalizationModal.connectToInternalDB": "Connect to my company’s internal databases", "personalizationModal.buildBackendServices": "Build backend services (endpoints)", "personalizationModal.manipulateFiles": "Manipulate/transfer files", "personalizationModal.specifyOtherSalesAndMarketingGoal": "Specify your other Sales and Marketing goals", "personalizationModal.registerEmailForTrial": "Register your email to activate a free 14 day trial of our {trial}", "personalizationModal.registerEmailForTrial.enterprise": "Enterprise features", "personalizationModal.registerEmailForTrial.notice": "By checking this box, you agree to let us store your name and email to activate your trial and send over your license key. We’ll check-in at the end of the trial to ensure you’re getting the most out of our Enterprise features.", "personalizationModal.registerEmailForTrial.success.title": "Your enterprise license is on its way", "personalizationModal.registerEmailForTrial.success.message": "You'll shortly receive an email to activate your enterprise license. If you don't see it, check your spam folder.", "personalizationModal.registerEmailForTrial.success.button": "Start using n8n", "personalizationModal.registerEmailForTrial.error": "Error while registering for enterprise trial", "pushConnection.nodeExecutedSuccessfully": "<PERSON><PERSON> executed successfully", "pushConnection.workflowExecutedSuccessfully": "Workflow executed successfully", "pushConnectionTracker.cannotConnectToServer": "You have a connection issue or the server is down. <br />n8n should reconnect automatically once the issue is resolved.", "pushConnectionTracker.connectionLost": "Connection lost", "pushConnectionTracker.connectionLost.message": "Attempting to reconnect...", "pushConnection.pollingNode.dataNotFound": "No {service} data found", "pushConnection.pollingNode.dataNotFound.message": "We didn’t find any data in {service} to simulate an event. Please create one in {service} and try again.", "pushConnection.executionFailed": "Execution failed", "pushConnection.executionFailed.message": "There might not be enough memory to finish the execution. Tips for avoiding this <a target=\"_blank\" href=\"https://docs.n8n.io/flow-logic/error-handling/memory-errors/\">here</a>", "pushConnection.executionError": "There was a problem executing the workflow{error}", "pushConnection.executionError.openNode": "Open errored node", "pushConnection.executionError.details": "<br /><strong>{details}</strong>", "prompts.productTeamMessage": "Our product team will get in touch personally", "prompts.npsSurvey.recommendationQuestion": "How likely are you to recommend n8n to a friend or colleague?", "prompts.npsSurvey.greatFeedbackTitle": "Great to hear! Can we reach out to see how we can make n8n even better for you?", "prompts.npsSurvey.defaultFeedbackTitle": "Thanks for your feedback! We'd love to understand how we can improve. Can we reach out?", "prompts.npsSurvey.notLikely": "Not likely", "prompts.npsSurvey.veryLikely": "Very likely", "prompts.npsSurvey.send": "Send", "prompts.npsSurvey.yourEmailAddress": "Your email address", "prompts.npsSurvey.reviewUs": "If you’d like to help even more, leave us a <a target=\"_blank\" href=\"https://www.g2.com/products/n8n/reviews/start\">review on G2</a>.", "prompts.npsSurvey.thanks": "Thanks for your feedback", "renameAction.emptyName.message": "Please enter a name, or press 'esc' to go back to the old one", "renameAction.emptyName.title": "Name missing", "renameAction.invalidName.title": "Invalid name", "resourceLocator.id.placeholder": "Enter ID...", "resourceLocator.mode.id": "By ID", "resourceLocator.mode.url": "By URL", "resourceLocator.mode.list": "From list", "resourceLocator.mode.list.disabled.title": "Change to Fixed mode to choose From List", "resourceLocator.mode.list.error.title": "Could not load list", "resourceLocator.mode.list.error.description.checkCredentials": "Check your credential", "resourceLocator.mode.list.error.description.noCredentials": "Add your credential", "resourceLocator.mode.list.noResults": "No results", "resourceLocator.mode.list.openUrl": "Open URL", "resourceLocator.mode.list.placeholder": "Choose...", "resourceLocator.mode.list.searchRequired": "Enter a search term to show results", "resourceLocator.mode.list.addNewResource.vectorStoreInMemory": "Create key '{resourceName}'", "resourceLocator.modeSelector.placeholder": "Mode...", "resourceLocator.openSpecificResource": "Open {entity} in {appName}", "resourceLocator.openResource": "Open in {appName}", "resourceLocator.placeholder.searchOrCreate": "Search or create...", "resourceLocator.placeholder.search": "Search...", "resourceLocator.url.placeholder": "Enter URL...", "resourceMapper.autoMappingNotice": "In this mode, make sure the incoming data fields are named the same as the {fieldWord} in {serviceName}. (Use an 'Edit Fields' node before this node to change them if required.)", "resourceMapper.mappingMode.label": "Mapping Column Mode", "resourceMapper.mappingMode.defineBelow.name": "Map Each Column Manually", "resourceMapper.mappingMode.defineBelow.description": "Set the value for each {fieldWord}", "resourceMapper.mappingMode.autoMapInputData.name": "Map Automatically", "resourceMapper.mappingMode.autoMapInputData.description": "Look for incoming data that matches the {fieldWord} in {serviceName}", "resourceMapper.fetchingFields.message": "Fetching {fieldWord}", "resourceMapper.fetchingFields.errorMessage": "Can't get {fieldWord}.", "resourceMapper.fetchingFields.noFieldsFound": "No {fieldWord} found in {serviceName}.", "resourceMapper.columnsToMatchOn.label": "{fieldWord} to match on", "resourceMapper.columnsToMatchOn.multi.description": "The {fieldWord} to use when matching rows in {nodeDisplayName} to the input items of this node. Usually an ID.", "resourceMapper.columnsToMatchOn.single.description": "The {fieldWord} to use when matching rows in {nodeDisplayName} to the input items of this node. Usually an ID.", "resourceMapper.columnsToMatchOn.tooltip": "The {fieldWord} to compare when finding the rows to update", "resourceMapper.columnsToMatchOn.noFieldsFound": "No {fieldWord} that can be used for matching found in {serviceName}.", "resourceMapper.valuesToSend.label": "Values to Send", "resourceMapper.valuesToUpdate.label": "Values to Update", "resourceMapper.usingToMatch": "(using to match)", "resourceMapper.usingToMatch.description": "This {fieldWord} won't be updated and can't be removed, as it's used for matching", "resourceMapper.removeField": "Remove {fieldWord}", "resourceMapper.mandatoryField.title": "This {fieldWord} is mandatory and can’t be removed", "resourceMapper.addFieldToSend": "Add {fieldWord} to send", "resourceMapper.matching.title": "This {fieldWord} is used for matching and can’t be removed", "resourceMapper.addAllFields": "Add All {fieldWord}", "resourceMapper.removeAllFields": "Remove All {fieldWord}", "resourceMapper.refreshFieldList": "Refresh {fieldWord} List", "resourceMapper.staleDataWarning.tooltip": "{fieldWord} are outdated. Refresh to see the changes.", "resourceMapper.staleDataWarning.notice": "Refresh to see the updated fields", "resourceMapper.attemptToConvertTypes.displayName": "Attempt To Convert Types", "resourceMapper.attemptToConvertTypes.description": "Attempt to convert types when mapping fields", "runData.openSubExecutionSingle": "View sub-execution", "runData.openSubExecutionWithId": "View sub-execution {id}", "runData.openParentExecution": "View parent execution {id}", "runData.emptyItemHint": "This is an item, but it's empty.", "runData.emptyArray": "[empty array]", "runData.emptyString": "[empty]", "runData.emptyObject": "[empty object]", "runData.unnamedField": "[Unnamed field]", "runData.switchToBinary.info": "This item only has", "runData.switchToBinary.binary": "binary data", "runData.linking.hint": "Link displayed input and output runs", "runData.unlinking.hint": "Unlink displayed input and output runs", "runData.binary": "Binary", "runData.copyItemPath": "Copy Item Path", "runData.copyItemPath.toast": "Item path copied", "runData.copyParameterPath": "Copy Parameter Path", "runData.copyParameterPath.toast": "Parameter path copied", "runData.copyValue": "Copy Selection", "runData.copyValue.toast": "Output data copied", "runData.copyToClipboard": "Copy to Clipboard", "runData.copyDisabled": "First click on the output data you want to copy, then click this button.", "runData.editOutput": "Edit Output", "runData.editOutputInvalid": "Problem with output data", "runData.editOutputInvalid.singleQuote": "Unexpected single quote. Please use double quotes (\") instead", "runData.editOutputInvalid.onLine": "On line {line}:", "runData.editOutputInvalid.atPosition": "(at position {position})", "runData.editValue": "Edit Value", "runData.executionStatus.success": "Executed successfully", "runData.executionStatus.failed": "Execution failed", "runData.downloadBinaryData": "Download", "runData.executeNode": "Test Node", "runData.executionTime": "Execution Time", "runData.fileExtension": "File Extension", "runData.directory": "Directory", "runData.fileName": "File Name", "runData.invalidPinnedData": "Invalid pinned data", "runData.items": "Items", "runData.json": "JSON", "runData.rendered": "Rendered", "runData.schema": "<PERSON><PERSON><PERSON>", "runData.mimeType": "Mime Type", "runData.fileSize": "File Size", "runData.ms": "ms", "runData.noBinaryDataFound": "No binary data found", "runData.noData": "No data", "runData.noTextDataFound": "No text data found", "runData.nodeReturnedALargeAmountOfData": "Node returned a large amount of data", "runData.output": "Output", "runData.showBinaryData": "View", "runData.startTime": "Start Time", "runData.table": "Table", "runData.table.viewSubExecution": "View sub-execution {id}", "runData.pindata.learnMore": "Learn more", "runData.pindata.thisDataIsPinned": "This data is pinned for test executions.", "runData.pindata.unpin": "Unpin", "runData.editor.save": "Save", "runData.editor.cancel": "Cancel", "runData.editor.copyDataInfo": "You can copy data from previous executions and paste it above.", "runData.aiContentBlock.startedAt": "Started at {startTime}", "runData.aiContentBlock.tokens": "{count} Tokens", "runData.aiContentBlock.tokens.prompt": "Prompt:", "runData.aiContentBlock.tokens.completion": "Completion:", "runData.trimmedData.title": "Data not viewable yet", "runData.trimmedData.message": "It will be available here once the execution has finished.", "runData.trimmedData.loading": "Loading data", "runData.panel.actions.collapse": "Collapse panel", "runData.panel.actions.open": "Open panel", "runData.panel.actions.popOut": "Pop out panel", "runData.panel.actions.sync": "Sync selection with canvas", "saveButton.save": "@:_reusableBaseText.save", "saveButton.saved": "Saved", "saveWorkflowButton.hint": "Save workflow", "saveButton.saving": "Saving", "settings": "Settings", "settings.communityNodes": "Community nodes", "settings.communityNodes.empty.title": "Supercharge your workflows with community nodes", "settings.communityNodes.empty.verified.only.title": "Supercharge your workflows with verified community nodes", "settings.communityNodes.empty.description": "Install over {count} node packages contributed by our community.", "settings.communityNodes.empty.verified.only.description": "You can install community and partner built node packages that have been verified by n8n directly from the nodes panel. Installed packages will show up here.", "settings.communityNodes.empty.description.no-packages": "Install node packages contributed by our community.", "settings.communityNodes.empty.installPackageLabel": "Install a community node", "settings.communityNodes.npmUnavailable.warning": "To use this feature, please <a href=\"{npmUrl}\" target=\"_blank\" title=\"How to install npm\">install npm</a> and restart n8n.", "settings.communityNodes.packageNodes.label": "{count} node | {count} nodes", "settings.communityNodes.updateAvailable.tooltip": "A newer version is available", "settings.communityNodes.viewDocsAction.label": "Documentation", "settings.communityNodes.uninstallAction.label": "Uninstall package", "settings.communityNodes.upToDate.tooltip": "You are up to date", "settings.communityNodes.failedToLoad.tooltip": "There is a problem with this package, try uninstalling it then reinstalling to resolve this issue", "settings.communityNodes.fetchError.title": "Problem fetching installed packages", "settings.communityNodes.fetchError.message": "There may be a problem with your internet connection or your n8n instance", "settings.communityNodes.installModal.title": "Install community nodes", "settings.communityNodes.installModal.description": "Find community nodes to add on the npm public registry.", "settings.communityNodes.browseButton.label": "Browse", "settings.communityNodes.installModal.packageName.label": "npm Package Name", "settings.communityNodes.installModal.packageName.tooltip": "<img src='/static/community_package_tooltip_img.png'/><p>This is the title of the package on <a href='{npmURL}'>npmjs.com</a></p><p>Install a specific version by adding it after {'@'}, e.g. <code>package-name{'@'}0.15.0</code></p>", "settings.communityNodes.installModal.packageName.placeholder": "e.g. n8n-nodes-chatwork", "settings.communityNodes.installModal.checkbox.label": "I understand the risks of installing unverified code from a public source.", "settings.communityNodes.installModal.installButton.label": "Install", "settings.communityNodes.installModal.installButton.label.loading": "Installing", "settings.communityNodes.installModal.error.packageNameNotValid": "Package name must start with n8n-nodes-", "settings.communityNodes.messages.install.success": "Package installed", "settings.communityNodes.messages.install.error": "Error installing new package", "settings.communityNodes.messages.uninstall.error": "Problem uninstalling package", "settings.communityNodes.messages.uninstall.success.title": "Package uninstalled", "settings.communityNodes.messages.update.success.title": "Package updated", "settings.communityNodes.messages.update.success.message": "{packageName} updated to version {version}", "settings.communityNodes.messages.update.error.title": "Problem updating package", "settings.communityNodes.confirmModal.uninstall.title": "Uninstall package?", "settings.communityNodes.confirmModal.uninstall.message": "Any workflows that use nodes from the {packageName} package won't be able to run. Are you sure?", "settings.communityNodes.confirmModal.uninstall.buttonLabel": "Uninstall package", "settings.communityNodes.confirmModal.uninstall.buttonLoadingLabel": "Uninstalling", "settings.communityNodes.confirmModal.update.title": "Update community node package?", "settings.communityNodes.confirmModal.update.message": "You are about to update {packageName} to version {version}", "settings.communityNodes.confirmModal.update.warning": "This version has not been verified by n8n and may contain breaking changes or bugs.", "settings.communityNodes.confirmModal.update.description": "We recommend you deactivate workflows that use any of the package's nodes and reactivate them once the update is completed", "settings.communityNodes.confirmModal.update.buttonLabel": "Update package", "settings.communityNodes.confirmModal.update.buttonLoadingLabel": "Updating...", "settings.goBack": "Go back", "settings.personal": "Personal", "settings.personal.basicInformation": "Basic Information", "settings.personal.personalSettings": "Personal Settings", "settings.personal.personalSettingsUpdated": "Personal details updated", "settings.personal.personalSettingsUpdatedError": "Problem updating your details", "settings.personal.role.tooltip.default": "Default role for new users", "settings.personal.role.tooltip.member": "Create and manage own workflows and credentials", "settings.personal.role.tooltip.admin": "Full access to manage workflows,tags, credentials, projects, users and more", "settings.personal.role.tooltip.owner": "Manage everything{cloudAccess}", "settings.personal.role.tooltip.cloud": " and access Cloud dashboard", "settings.personal.save": "Save", "settings.personal.security": "Security", "settings.signup.signUpInviterInfo": "{firstName} {lastName} has invited you to n8n", "settings.users": "Users", "settings.users.search.placeholder": "Search by name or email", "settings.users.confirmDataHandlingAfterDeletion": "What should we do with their data?", "settings.users.confirmUserDeletion": "Are you sure you want to delete this invited user?", "settings.users.delete": "Delete", "settings.users.deleteConfirmationMessage": "Type \"delete all data\" to confirm", "settings.users.deleteConfirmationText": "delete all data", "settings.users.deleteUser": "Delete {user}", "settings.users.actions.delete": "Delete User", "settings.users.actions.reinvite": "Resend Invite", "settings.users.actions.copyInviteLink": "Copy Invite Link", "settings.users.actions.copyPasswordResetLink": "Copy Password Reset Link", "settings.users.actions.allowSSOManualLogin": "Allow Manual Login", "settings.users.actions.disallowSSOManualLogin": "Disallow Manual Login", "settings.users.deleteWorkflowsAndCredentials": "Delete their workflows and credentials", "settings.users.emailInvitesSent": "An invite email was sent to {emails}", "settings.users.emailInvitesSentError": "Could not invite {emails}", "settings.users.emailSentTo": "Email sent to {email}", "settings.users.invalidEmailError": "{email} is not a valid email", "settings.users.inviteLink.copy": "Copy Invite Link", "settings.users.inviteLink.error": "Could not retrieve invite link", "settings.users.invite": "Invite", "settings.users.invite.tooltip": "SAML login is activated. Users should be created in the IdP and will be provisioned in n8n on their first sign on.", "settings.users.inviteNewUsers": "Invite new users", "settings.users.copyInviteUrls": "You can now send the invitation links directly to your users", "settings.users.inviteResent": "Invite resent", "settings.users.inviteUser": "Invite user", "settings.users.inviteUser.inviteUrl": "Create invite link", "settings.users.inviteXUser": "Invite {count} users", "settings.users.inviteXUser.inviteUrl": "Create {count} invite links", "settings.users.inviteUrlCreated": "Invite link copied to clipboard", "settings.users.inviteUrlCreated.message": "Send the invite link to your invitee for activation", "settings.users.passwordResetUrlCreated": "Password reset link copied to clipboard", "settings.users.passwordResetUrlCreated.message": "Send the reset link to your user for them to reset their password", "settings.users.passwordResetLinkError": "Could not retrieve password reset link", "settings.users.allowSSOManualLogin": "Manual Login Allowed", "settings.users.allowSSOManualLogin.message": "User can now login manually and through SSO", "settings.users.disallowSSOManualLogin": "Manual Login Disallowed", "settings.users.disallowSSOManualLogin.message": "User must now login through SSO only", "settings.users.multipleInviteUrlsCreated": "Invite links created", "settings.users.multipleInviteUrlsCreated.message": "Send the invite links to your invitees for activation", "settings.users.newEmailsToInvite": "New User Email Addresses", "settings.users.noUsersToInvite": "No users to invite", "settings.users.setupMyAccount": "Set up my owner account", "settings.users.setupToInviteUsers": "To invite users, set up your own account", "settings.users.setupToInviteUsersInfo": "Invited users won’t be able to see workflows and credentials of other users unless you upgrade. <a href=\"https://docs.n8n.io/user-management/\" target=\"_blank\">More info</a> <br /> <br />", "settings.users.smtpToAddUsersWarning": "Set up SMTP before adding users (so that n8n can send them invitation emails). <a target=\"_blank\" href=\"https://docs.n8n.io/hosting/authentication/user-management-self-hosted/\">Instructions</a>", "settings.users.transferWorkflowsAndCredentials": "Transfer their workflows and credentials to another user or project", "settings.users.transferWorkflowsAndCredentials.user": "User or project to transfer to", "settings.users.transferWorkflowsAndCredentials.placeholder": "Select project or user", "settings.users.transferredToUser": "Data transferred to {projectName}", "settings.users.userNotFound": "User not found", "settings.users.userDeleted": "User deleted", "settings.users.userDeletedError": "Problem while deleting user", "settings.users.userInvited": "User invited", "settings.users.userInvitedError": "User could not be invited", "settings.users.userReinviteError": "Could not reinvite user", "settings.users.userToTransferTo": "User to transfer to", "settings.users.usersEmailedError": "Couldn't send invite email", "settings.users.usersInvited": "Users invited", "settings.users.usersInvitedError": "Could not invite users", "settings.users.advancedPermissions.warning": "{link} to unlock the ability to create additional admin users", "settings.users.advancedPermissions.warning.link": "Upgrade", "settings.users.userRoleUpdated": "Changes saved", "settings.users.userRoleUpdated.message": "{user} has been successfully updated to a {role}", "settings.users.userRoleUpdatedError": "Unable to updated role", "settings.users.table.update.error": "Failed to update table", "settings.users.table.header.user": "@:_reusableBaseText.user", "settings.users.table.header.accountType": "Account Type", "settings.users.table.header.2fa": "2FA", "settings.users.table.header.lastActive": "Last Active", "settings.users.table.row.allProjects": "All projects", "settings.users.table.row.personalProject": "Personal project", "settings.users.table.row.deleteUser": "Remove user", "settings.users.table.row.role.description.admin": "Full access to all workflows, credentials, projects, users and more", "settings.users.table.row.role.description.member": "Manage and create own workflows and credentials", "settings.users.table.row.2fa.enabled": "@:_reusableBaseText.enabled", "settings.users.table.row.2fa.disabled": "@:_reusableBaseText.disabled", "settings.api": "API", "settings.api.scopes.upgrade": "{link} to unlock the ability to modify API key scopes", "settings.api.scopes.upgrade.link": "Upgrade", "settings.n8napi": "n8n API", "settings.log-streaming": "Log Streaming", "settings.log-streaming.heading": "Log Streaming", "settings.log-streaming.add": "Add new destination", "settings.log-streaming.actionBox.title": "Available on the Enterprise plan", "settings.log-streaming.actionBox.description": "Log Streaming is available as a paid feature. Learn more about it.", "settings.log-streaming.actionBox.button": "See plans", "settings.log-streaming.infoText": "Send logs to external endpoints of your choice. You can also write logs to a file or the console using environment variables. <a href=\"https://docs.n8n.io/log-streaming/\" target=\"_blank\">More info</a>", "settings.log-streaming.addFirstTitle": "Set up a destination to get started", "settings.log-streaming.addFirst": "Add your first destination by clicking on the button and selecting a destination type.", "settings.log-streaming.saving": "Saving", "settings.log-streaming.delete": "Delete", "settings.log-streaming.continue": "Continue", "settings.log-streaming.selecttype": "Select type to create", "settings.log-streaming.selecttypehint": "Select the type for the new log stream destination", "settings.log-streaming.tab.settings": "Settings", "settings.log-streaming.tab.events": "Events", "settings.log-streaming.tab.events.title": "Select groups or single events to subscribe to:", "settings.log-streaming.tab.events.anonymize": "Anonymize sensitive data", "settings.log-streaming.tab.events.anonymize.info": "Fields containing personal information like name or email are anonymized", "settings.log-streaming.eventGroup.n8n.ai": "AI node logs", "settings.log-streaming.eventGroup.n8n.audit": "Audit Events", "settings.log-streaming.eventGroup.n8n.audit.info": "Will send events when user details or other audit data changes", "settings.log-streaming.eventGroup.n8n.workflow": "Workflow Events", "settings.log-streaming.eventGroup.n8n.workflow.info": "Will send workflow execution events", "settings.log-streaming.eventGroup.n8n.user": "User", "settings.log-streaming.eventGroup.n8n.node": "Node Executions", "settings.log-streaming.eventGroup.n8n.node.info": "Will send step-wise execution events every time a node executes. Please note that this can lead to a high frequency of logged events and is probably not suitable for general use.", "settings.log-streaming.eventGroup.n8n.runner": "Runner tasks", "settings.log-streaming.eventGroup.n8n.runner.info": "Will send an event when a Code node execution is requested from a task runner, and when a response is received from the runner with the result.", "settings.log-streaming.eventGroup.n8n.queue": "Queue events", "settings.log-streaming.eventGroup.n8n.queue.info": "Will send an event when a queue-related event occurs, e.g. enqueuing, dequeueing, completion, failure, or stalling.", "settings.log-streaming.eventGroup.n8n.worker": "Worker", "settings.log-streaming.$$AbstractMessageEventBusDestination": "Generic", "settings.log-streaming.$$MessageEventBusDestinationWebhook": "Webhook", "settings.log-streaming.$$MessageEventBusDestinationSentry": "Sentry", "settings.log-streaming.$$MessageEventBusDestinationRedis": "Redis", "settings.log-streaming.$$MessageEventBusDestinationSyslog": "Syslog", "settings.log-streaming.destinationDelete.cancelButtonText": "", "settings.log-streaming.destinationDelete.confirmButtonText": "Yes, delete", "settings.log-streaming.destinationDelete.headline": "Delete Destination?", "settings.log-streaming.destinationDelete.message": "Are you sure that you want to delete '{destinationName}'?", "settings.log-streaming.addDestination": "Add new destination", "settings.log-streaming.destinations": "Log destinations", "settings.api.trial.upgradePlan.title": "Upgrade to use API", "settings.api.trial.upgradePlan.description": "To prevent abuse, we limit API access to your workspace during your trial. If this is hindering your evaluation of n8n, please contact <a href=\"mailto:support{'@'}n8n.io\">support{'@'}n8n.io</a>", "settings.api.trial.upgradePlan.cta": "Upgrade plan", "settings.api.create.description": "Control n8n programmatically using the <a href=\"https://docs.n8n.io/api\" target=\"_blank\">n8n API</a>", "settings.api.create.button": "Create an API Key", "settings.api.create.button.loading": "Creating API Key...", "settings.api.create.error": "API Key creation failed.", "settings.api.edit.error": "API Key update failed.", "settings.api.delete.title": "Delete this API Key?", "settings.api.delete.description": "Any application using this API Key will no longer have access to n8n. This operation cannot be undone.", "settings.api.delete.button": "Delete Forever", "settings.api.delete.error": "Deleting the API Key failed.", "settings.api.delete.toast": "API Key deleted", "settings.api.create.toast": "API Key created", "settings.api.update.toast": "API Key updated", "settings.api.creationTime": "API key was created on {time}", "settings.api.expirationTime": "Expires on {time}", "settings.api.expired": "This API key has expired", "settings.api.neverExpires": "Never expires", "settings.api.view.copy.toast": "API Key copied to clipboard", "settings.api.view.apiPlayground": "API Playground", "settings.api.view.info": "Use your API Key to control n8n programmatically using the {apiAction}. But if you only want to trigger workflows, consider using the {webhookAction} instead.", "settings.api.view.copy": "Make sure to copy your API key now as you will not be able to see this again.", "settings.api.view.info.api": "n8n API", "settings.api.view.info.webhook": "webhook node", "settings.api.view.tryapi": "Try it out using the", "settings.api.view.more-details": "You can find more details in", "settings.api.view.external-docs": "the API documentation", "settings.api.view.error": "Could not check if an api key already exists.", "settings.api.view.modal.form.label": "Label", "settings.api.view.modal.form.expiration": "Expiration", "settings.api.view.modal.form.expirationText": "The API key will expire on {expirationDate}", "settings.api.view.modal.form.label.placeholder": "e.g Internal Project", "settings.api.view.modal.form.expiration.custom": "Custom", "settings.api.view.modal.form.expiration.days": "{numberOfDays} days", "settings.api.view.modal.form.expiration.none": "No Expiration", "settings.api.view.modal.title.created": "API Key Created", "settings.api.view.modal.title.create": "Create API Key", "settings.api.view.modal.title.edit": "Edit API Key", "settings.api.view.modal.done.button": "Done", "settings.api.view.modal.save.button": "Save", "settings.api.scopes.placeholder": "Select", "settings.api.scopes.selectAll": "Select All", "settings.api.scopes.label": "<PERSON><PERSON><PERSON>", "settings.version": "Version", "settings.usageAndPlan.title": "Usage and plan", "settings.usageAndPlan.description": "You’re on the {name} {type}", "settings.usageAndPlan.plan": "Plan", "settings.usageAndPlan.callOut": "{link} selected paid features for free (forever)", "settings.usageAndPlan.callOut.link": "Unlock", "settings.usageAndPlan.edition": "Edition", "settings.usageAndPlan.error": "@:_reusableBaseText.error", "settings.usageAndPlan.activeWorkflows": "Active workflows", "settings.usageAndPlan.activeWorkflows.unlimited": "@:_reusableBaseText.unlimited", "settings.usageAndPlan.activeWorkflows.count": "{count} of {limit}", "settings.usageAndPlan.activeWorkflows.hint": "Active workflows with multiple triggers count multiple times", "settings.usageAndPlan.button.activation": "Enter activation key", "settings.usageAndPlan.button.plans": "View plans", "settings.usageAndPlan.button.manage": "Manage plan", "settings.usageAndPlan.dialog.activation.title": "Enter activation key", "settings.usageAndPlan.dialog.activation.label": "Activation key", "settings.usageAndPlan.dialog.activation.activate": "@:_reusableBaseText.activate", "settings.usageAndPlan.dialog.activation.cancel": "@:_reusableBaseText.cancel", "settings.usageAndPlan.license.activation.error.title": "Activation failed", "settings.usageAndPlan.license.activation.success.title": "License activated", "settings.usageAndPlan.license.activation.success.message": "Your {name} {type} has been successfully activated.", "settings.usageAndPlan.license.communityRegistered.tooltip": "You have registered your email to unlock additional features on your community plan", "settings.externalSecrets.title": "External Secrets", "settings.externalSecrets.info": "Connect external secrets tools for centralized credentials management across environments, and to enhance system security.", "settings.externalSecrets.info.link": "More info", "settings.externalSecrets.actionBox.title": "Available on the Enterprise plan", "settings.externalSecrets.actionBox.description": "Connect external secrets tools for centralized credentials management across instances. {link}", "settings.externalSecrets.actionBox.description.link": "More info", "settings.externalSecrets.actionBox.buttonText": "See plans", "settings.externalSecrets.card.setUp": "Set Up", "settings.externalSecrets.card.deprecated": "deprecated", "settings.externalSecrets.card.secretsCount": "{count} secrets", "settings.externalSecrets.card.connectedAt": "Connected {date}", "settings.externalSecrets.card.connected": "Enabled", "settings.externalSecrets.card.disconnected": "Disabled", "settings.externalSecrets.card.actionDropdown.setup": "Edit connection", "settings.externalSecrets.card.actionDropdown.reload": "Reload secrets", "settings.externalSecrets.card.reload.success.title": "Reloaded successfully", "settings.externalSecrets.card.reload.success.description": "All secrets have been reloaded from {provider}.", "settings.externalSecrets.provider.title": "Commit and push changes", "settings.externalSecrets.provider.description": "Select the files you want to stage in your commit and add a commit message. ", "settings.externalSecrets.provider.buttons.cancel": "Cancel", "settings.externalSecrets.provider.buttons.save": "Save", "settings.externalSecrets.provider.buttons.saving": "Saving", "settings.externalSecrets.card.connectedSwitch.title": "Enable {provider}", "settings.externalSecrets.provider.save.success.title": "Provider settings saved successfully", "settings.externalSecrets.provider.connected.success.title": "Provider connected successfully", "settings.externalSecrets.provider.disconnected.success.title": "Provider disconnected successfully", "settings.externalSecrets.provider.testConnection.success.connected": "Service enabled, {count} secrets available on {provider}.", "settings.externalSecrets.provider.testConnection.success.connected.usage": "Use secrets in credentials by setting a parameter to an expression and typing: {code}. ", "settings.externalSecrets.provider.testConnection.success.connected.docs": "More info", "settings.externalSecrets.provider.testConnection.success": "Connection to {provider} executed successfully. Enable the service to use the secrets in credentials.", "settings.externalSecrets.provider.testConnection.error.connected": "Connection unsuccessful, please check your {provider} settings", "settings.externalSecrets.provider.testConnection.error": "Connection unsuccessful, please check your {provider} settings", "settings.externalSecrets.provider.closeWithoutSaving.title": "Close without saving?", "settings.externalSecrets.provider.closeWithoutSaving.description": "Are you sure you want to throw away the changes you made to the {provider} settings?", "settings.externalSecrets.provider.closeWithoutSaving.cancel": "Close", "settings.externalSecrets.provider.closeWithoutSaving.confirm": "Keep editing", "settings.externalSecrets.docs": "https://docs.n8n.io/external-secrets/", "settings.externalSecrets.docs.use": "https://docs.n8n.io/external-secrets/#use-secrets-in-n8n-credentials", "settings.sourceControl.title": "Environments", "settings.sourceControl.actionBox.title": "Available on the Enterprise plan", "settings.sourceControl.actionBox.description": "Use multiple instances for different environments (dev, prod, etc.), deploying between them via a Git repository.", "settings.sourceControl.actionBox.description.link": "More info", "settings.sourceControl.actionBox.buttonText": "See plans", "settings.sourceControl.connection.error": "Source control failed to connect", "settings.sourceControl.connection.error.message": "We couldn't find the repository connected to this instance. Please double-check your {link} on this instance.", "settings.sourceControl.connection.error.link": "Git configuration", "settings.sourceControl.description": "Use multiple instances for different environments (dev, prod, etc.), deploying between them via a Git repository. {link}", "settings.sourceControl.description.link": "More info", "settings.sourceControl.gitConfig": "Git configuration", "settings.sourceControl.repoUrl": "Git repository URL (SSH)", "settings.sourceControl.repoUrlPlaceholder": "e.g. git{'@'}github.com:my-team/my-repository", "settings.sourceControl.repoUrlInvalid": "The Git repository URL is not valid", "settings.sourceControl.authorName": "Commit author  name", "settings.sourceControl.authorEmail": "Commit author  email", "settings.sourceControl.authorEmailInvalid": "The provided email is not correct", "settings.sourceControl.sshKey": "SSH Key", "settings.sourceControl.sshKeyDescription": "Paste the SSH key in your git repository/account settings. {link}", "settings.sourceControl.sshKeyDescriptionLink": "More info", "settings.sourceControl.refreshSshKey": "Refresh Key", "settings.sourceControl.refreshSshKey.successful.title": "SSH Key refreshed successfully", "settings.sourceControl.refreshSshKey.error.title": "SSH Key refresh failed", "settings.sourceControl.button.continue": "Continue", "settings.sourceControl.button.connect": "Connect", "settings.sourceControl.button.disconnect": "Disconnect Git", "settings.sourceControl.button.save": "Save settings", "settings.sourceControl.instanceSettings": "Instance settings", "settings.sourceControl.branches": "Branch connected to this n8n instance", "settings.sourceControl.protected": "{bold}: prevent editing workflows (recommended for production environments).", "settings.sourceControl.protected.bold": "Protected instance", "settings.sourceControl.color": "Color", "settings.sourceControl.switchBranch.title": "Switch to {branch} branch", "settings.sourceControl.switchBranch.description": "Please confirm you want to switch the current n8n instance to the branch: {branch}", "settings.sourceControl.sync.prompt.title": "Sync changes in {branch} branch", "settings.sourceControl.sync.prompt.description": "All the changes on your n8n instances will be synced with branch {branch} on the remote git repository. The following git sequence will be executed: pull > commit > push.", "settings.sourceControl.sync.prompt.placeholder": "Commit message", "settings.sourceControl.sync.prompt.error": "Please enter a commit message", "settings.sourceControl.button.push": "<PERSON><PERSON>", "settings.sourceControl.button.pull": "<PERSON><PERSON>", "settings.sourceControl.button.pull.forbidden": "Only the instance owner or instance admins can pull changes", "settings.sourceControl.button.push.forbidden": "You can't push changes from a protected instance", "settings.sourceControl.modals.push.title": "Commit and push changes", "settings.sourceControl.modals.push.description": "The following will be committed: ", "settings.sourceControl.modals.push.description.learnMore": "More info", "settings.sourceControl.modals.push.filesToCommit": "Files to commit", "settings.sourceControl.modals.push.filter": "Filters are applied. Showing {count} {entity}.", "settings.sourceControl.modals.push.workflowsToCommit": "Select workflows", "settings.sourceControl.modals.push.everythingIsUpToDate": "Everything is up to date", "settings.sourceControl.modals.push.noWorkflowChanges": "There are no workflow changes but the following will be committed: {link}", "settings.sourceControl.modals.push.noWorkflowChanges.moreInfo": "More info", "settings.sourceControl.modals.push.commitMessage": "Commit message", "settings.sourceControl.modals.push.commitMessage.placeholder": "e.g. My commit", "settings.sourceControl.modals.push.buttons.cancel": "Cancel", "settings.sourceControl.modals.push.buttons.save": "Commit and push", "settings.sourceControl.modals.push.success.title": "Pushed successfully", "settings.sourceControl.modals.push.success.description": "were committed and pushed to your remote repository", "settings.sourceControl.modals.push.projectAdmin.callout": "If you want to push workflows from your personal space, move then to a project first.", "settings.sourceControl.status.modified": "Modified", "settings.sourceControl.status.deleted": "Deleted", "settings.sourceControl.status.created": "New", "settings.sourceControl.status.renamed": "<PERSON>amed", "settings.sourceControl.pull.oneLastStep.title": "One last step", "settings.sourceControl.pull.oneLastStep.description": "You have new creds/vars. Fill them out to make sure your workflows function properly", "settings.sourceControl.pull.success.title": "Pulled successfully", "settings.sourceControl.pull.upToDate.title": "Up to date", "settings.sourceControl.pull.upToDate.description": "No workflow changes to pull from Git", "settings.sourceControl.pull.upToDate.variables.title": "Finish setting up your new variables to use in workflows", "settings.sourceControl.pull.upToDate.variables.description": "Review Variables", "settings.sourceControl.pull.upToDate.credentials.title": "Finish setting up your new credentials to use in workflows", "settings.sourceControl.pull.upToDate.credentials.description": "Review Credentials", "settings.sourceControl.modals.pull.title": "Pull changes", "settings.sourceControl.modals.pull.description": "These resources will be updated or deleted, and any local changes to them will be lost. To keep the local version, push it before pulling.", "settings.sourceControl.modals.pull.description.learnMore": "More info", "settings.sourceControl.modals.pull.buttons.cancel": "@:_reusableBaseText.cancel", "settings.sourceControl.modals.pull.buttons.save": "Pull and override", "settings.sourceControl.modals.disconnect.title": "Disconnect Git repository", "settings.sourceControl.modals.disconnect.message": "Please confirm you want to disconnect this n8n instance from the Git repository", "settings.sourceControl.modals.disconnect.confirm": "Disconnect Git", "settings.sourceControl.modals.disconnect.cancel": "@:_reusableBaseText.cancel", "settings.sourceControl.modals.refreshSshKey.title": "Refresh SSH Key", "settings.sourceControl.modals.refreshSshKey.message": "This will delete the current SSH key and create a new one. You will not be able to authenticate with the current key anymore.", "settings.sourceControl.modals.refreshSshKey.cancel": "Cancel", "settings.sourceControl.modals.refreshSshKey.confirm": "Refresh key", "settings.sourceControl.loading.connecting": "Connecting", "settings.sourceControl.toast.connected.title": "Git repository connected", "settings.sourceControl.toast.connected.message": "Select the branch to complete the configuration", "settings.sourceControl.toast.connected.error": "Error connecting to Git", "settings.sourceControl.toast.disconnected.title": "Git repository disconnected", "settings.sourceControl.toast.disconnected.message": "You can no longer sync your instance with the remote repository", "settings.sourceControl.toast.disconnected.error": "Error disconnecting from Git", "settings.sourceControl.loading.pull": "Pulling from remote", "settings.sourceControl.loading.checkingForChanges": "Checking for changes", "settings.sourceControl.loading.push": "Pushing to remote", "settings.sourceControl.lastUpdated": "Last updated {date} at {time}", "settings.sourceControl.saved.title": "<PERSON><PERSON><PERSON> successfully saved", "settings.sourceControl.refreshBranches.tooltip": "Reload branches list", "settings.sourceControl.refreshBranches.success": "Branches successfully refreshed", "settings.sourceControl.refreshBranches.error": "Error refreshing branches", "settings.sourceControl.docs.url": "https://docs.n8n.io/source-control-environments/", "settings.sourceControl.docs.setup.url": "https://docs.n8n.io/source-control-environments/setup/", "settings.sourceControl.docs.setup.ssh.url": "https://docs.n8n.io/source-control-environments/setup/#step-3-set-up-a-deploy-key", "settings.sourceControl.docs.using.url": "https://docs.n8n.io/source-control-environments/using/", "settings.sourceControl.docs.using.pushPull.url": "https://docs.n8n.io/source-control-environments/using/push-pull", "settings.sourceControl.error.not.connected.title": "Environments have not been enabled", "settings.sourceControl.error.not.connected.message": "Please head over to <a href='/settings/environments'>environment settings</a> to connect a git repository first to activate this functionality.", "showMessage.cancel": "@:_reusableBaseText.cancel", "showMessage.ok": "OK", "showMessage.showDetails": "Show Details", "startupError": "Error connecting to n8n", "startupError.message": "Could not connect to server. <a data-action='reload'>Refresh</a> to try again", "tagsDropdown.createTag": "Create tag \"{filter}\"", "tagsDropdown.manageTags": "Manage tags", "tagsDropdown.noMatchingTagsExist": "No matching tags exist", "tagsDropdown.noTagsExist": "No tags exist", "tagsDropdown.showError.message": "A problem occurred when trying to create the '{name}' tag", "tagsDropdown.showError.title": "Could not create tag", "tagsDropdown.typeToCreateATag": "Type to create a tag", "tagsManager.couldNotDeleteTag": "Could not delete tag", "tagsManager.done": "Done", "tagsManager.manageTags": "Manage tags", "tagsManager.showError.onFetch.title": "Could not fetch tags", "tagsManager.showError.onFetch.message": "A problem occurred when trying to fetch tags", "tagsManager.showError.onCreate.message": "A problem occurred when trying to create the tag '{escapedName}'", "tagsManager.showError.onCreate.title": "Could not create tag", "tagsManager.showError.onDelete.message": "A problem occurred when trying to delete the tag '{escapedName}'", "tagsManager.showError.onDelete.title": "Could not delete tag", "tagsManager.showError.onUpdate.message": "A problem occurred when trying to update the tag '{escapedName}'", "tagsManager.showError.onUpdate.title": "Could not update tag", "tagsManager.showMessage.onDelete.title": "Tag deleted", "tagsManager.showMessage.onUpdate.title": "Tag updated", "tagsManager.tagNameCannotBeEmpty": "Tag name cannot be empty", "tagsTable.areYouSureYouWantToDeleteThisTag": "Are you sure you want to delete this tag?", "tagsTable.cancel": "@:_reusableBaseText.cancel", "tagsTable.createTag": "Create tag", "tagsTable.deleteTag": "Delete tag", "tagsTable.editTag": "Edit Tag", "tagsTable.name": "@:_reusableBaseText.name", "tagsTable.noMatchingTagsExist": "No matching tags exist", "tagsTable.saveChanges": "Save changes?", "tagsTable.usage": "Usage", "tagsTableHeader.addNew": "Add new", "tagsTableHeader.searchTags": "Search Tags", "tagsView.inUse": "{count} workflow | {count} workflows", "tagsView.notBeingUsed": "Not being used", "onboarding.title": "Demo: {name}", "template.buttons.goBackButton": "Go back", "template.buttons.useThisWorkflowButton": "Use this workflow", "template.details.appsInTheCollection": "This collection features", "template.details.appsInTheWorkflow": "Apps in this workflow", "template.details.by": "by", "template.details.categories": "Categories", "template.details.created": "Created", "template.details.details": "Details", "template.details.times": "times", "template.details.viewed": "Viewed", "template.byAuthor": "By {name}", "templates.allCategories": "All Categories", "templates.categoriesHeading": "Categories", "templates.collection": "Collection", "templates.collections": "Collections", "templates.collectionsNotFound": "Collection could not be found", "templates.connectionWarning": "⚠️ There was a problem fetching workflow templates. Check your internet connection.", "templates.heading": "Workflow templates", "templates.shareWorkflow": "Share template", "templates.noSearchResults": "Nothing found. Try adjusting your search to see more.", "templates.searchPlaceholder": "Search workflows", "templates.workflows": "Workflows", "templates.workflowsNotFound": "Workflow could not be found", "textEdit.edit": "Edit", "timeAgo.daysAgo": "%s days ago", "timeAgo.hoursAgo": "%s hours ago", "timeAgo.inDays": "in %s days", "timeAgo.inHours": "in %s hours", "timeAgo.inMinutes": "in %s minutes", "timeAgo.inMonths": "in %s months", "timeAgo.inOneDay": "in 1 day", "timeAgo.inOneHour": "in 1 hour", "timeAgo.inOneMinute": "in 1 minute", "timeAgo.inOneMonth": "in 1 month", "timeAgo.inOneWeek": "in 1 week", "timeAgo.inOneYear": "in 1 year", "timeAgo.inWeeks": "in %s weeks", "timeAgo.inYears": "in %s years", "timeAgo.justNow": "Just now", "timeAgo.minutesAgo": "%s minutes ago", "timeAgo.monthsAgo": "%s months ago", "timeAgo.oneDayAgo": "1 day ago", "timeAgo.oneHourAgo": "1 hour ago", "timeAgo.oneMinuteAgo": "1 minute ago", "timeAgo.oneMonthAgo": "1 month ago", "timeAgo.oneWeekAgo": "1 week ago", "timeAgo.oneYearAgo": "1 year ago", "timeAgo.rightNow": "Right now", "timeAgo.weeksAgo": "%s weeks ago", "timeAgo.yearsAgo": "%s years ago", "nodeIssues.credentials.notSet": "Credentials for {type} are not set.", "nodeIssues.credentials.notAvailable": "Credential is not available", "nodeIssues.credentials.doNotExist": "Credentials with name {name} do not exist for {type}.", "nodeIssues.credentials.doNotExist.hint": "You can create credentials with the exact name and then they get auto-selected on refresh..", "nodeIssues.credentials.notIdentified": "Credentials with name {name} exist for {type}.", "nodeIssues.credentials.notIdentified.hint": "Credentials are not clearly identified. Please select the correct credentials.", "nodeIssues.input.missing": "No node connected to required input \"{inputName}\"", "ndv.trigger.moreInfo": "More info", "ndv.trigger.copiedTestUrl": "Test URL copied to clipboard", "ndv.trigger.webhookBasedNode.executionsHelp.inactive": "<b>While building your workflow</b>, click the 'execute step' button, then go to {service} and make an event happen. This will trigger an execution, which will show up in this editor.<br /> <br /> <b>Once you're happy with your workflow</b>, <a data-key=\"activate\">activate</a> it. Then every time there's a matching event in {service}, the workflow will execute. These executions will show up in the <a data-key=\"executions\">executions list</a>, but not in the editor.", "ndv.trigger.webhookBasedNode.executionsHelp.active": "<b>While building your workflow</b>, click the 'execute step' button, then go to {service} and make an event happen. This will trigger an execution, which will show up in this editor.<br /> <br /> <b>Your workflow will also execute automatically</b>, since it's activated. Every time there’s a matching event in {service}, this node will trigger an execution. These executions will show up in the <a data-key=\"executions\">executions list</a>, but not in the editor. ", "ndv.trigger.webhookNode.listening": "Listening for test event", "ndv.trigger.chatTrigger.openChat": "Open Chat Window", "ndv.trigger.webhookNode.formTrigger.listening": "Listening for a test form submission", "ndv.trigger.webhookBasedNode.listening": "Listening for your trigger event", "ndv.trigger.webhookNode.requestHint": "Make a {type} request to:", "ndv.trigger.webhookBasedNode.serviceHint": "Go to {service} and create an event", "ndv.trigger.webhookBasedNode.chatTrigger.serviceHint": "Send a message in the chat", "ndv.trigger.webhookBasedNode.formTrigger.serviceHint": "Submit the test form that just opened in a new tab", "ndv.trigger.webhookBasedNode.activationHint.inactive": "Once you’ve finished building your workflow, <a data-key=\"activate\">activate it</a> to have it also listen continuously (you just won’t see those executions here).", "ndv.trigger.webhookBasedNode.activationHint.active": "This node will also trigger automatically on new {service} events (but those executions won’t show up here).", "ndv.trigger.pollingNode.activationHint.inactive": "Once you’ve finished building your workflow, <a data-key=\"activate\">activate it</a> to have it also check for events regularly (you just won’t see those executions here).", "ndv.trigger.pollingNode.activationHint.active": "This node will also trigger automatically on new {service} events (but those executions won’t show up here).", "ndv.trigger.executionsHint.question": "When will this node trigger my flow?", "ndv.trigger.pollingNode.fetchingEvent": "Fetching event", "ndv.trigger.pollingNode.fetchingHint": "This node is looking for an event in {name} that is similar to the one you defined", "ndv.trigger.pollingNode.executionsHelp.inactive": "<b>While building your workflow</b>, click the 'fetch' button to fetch a single mock event. It will show up in this editor.<br /><br /><b>Once you're happy with your workflow</b>, <a data-key=\"activate\">activate</a> it. Then n8n will regularly check {service} for new events, and execute this workflow if it finds any. These executions will show up in the <a data-key=\"executions\">executions list</a>, but not in the editor.", "ndv.trigger.pollingNode.executionsHelp.active": "<b>While building your workflow</b>, click the 'fetch' button to fetch a single mock event. It will show up in this editor.<br /><br /><b>Your workflow will also execute automatically</b>, since it's activated. n8n will regularly check {app_name} for new events, and execute this workflow if it finds any. These executions will show up in the <a data-key=\"executions\">executions list</a>, but not in the editor.", "ndv.trigger.webhookBasedNode.action": "Pull in events from {name}", "ndv.search.placeholder.output": "Search output", "ndv.search.placeholder.input": "Search selected node", "ndv.search.placeholder.input.schema": "Search previous nodes' fields", "ndv.search.noMatch.title": "No matching items", "ndv.search.noNodeMatch.title": "No matching fields", "ndv.search.noMatch.description": "Try changing or {link} the filter to see more", "ndv.search.noMatch.description.link": "clearing", "ndv.search.noMatchSchema.description": "To search field values, switch to table or JSON view. {link}", "ndv.search.noMatchSchema.description.link": "Clear filter", "ndv.search.items": "{matched} of {count} item | {matched} of {count} items", "updatesPanel.andIs": "and is", "updatesPanel.behindTheLatest": "behind the latest and greatest n8n", "updatesPanel.howToUpdateYourN8nVersion": "How to update your n8n version", "updatesPanel.version": "{numberOfVersions} version{howManySuffix}", "updatesPanel.weVeBeenBusy": "We’ve been busy ✨", "updatesPanel.youReOnVersion": "You’re on {currentVersionName}, which was released", "versionCard.breakingChanges": "Breaking changes", "versionCard.released": "Released", "versionCard.securityUpdate": "Security update", "versionCard.thisVersionHasASecurityIssue": "This version has a security issue.<br />It is listed here for completeness.", "versionCard.unknown": "unknown", "versionCard.version": "Version", "workflowActivator.workflowIsActive": "Workflow is already active", "workflowActivator.activateWorkflow": "Activate workflow", "workflowActivator.deactivateWorkflow": "Deactivate workflow", "workflowActivator.active": "Active", "workflowActivator.inactive": "Inactive", "workflowActivator.showError.title": "Workflow could not be {newStateName}", "workflowActivator.showMessage.activeChangedNodesIssuesExistTrue.message": "Please resolve outstanding issues before you activate it", "workflowActivator.showMessage.activeChangedNodesIssuesExistTrue.title": "Problem activating workflow", "workflowActivator.showMessage.activeChangedWorkflowIdUndefined.message": "Please save it before activating", "workflowActivator.showMessage.activeChangedWorkflowIdUndefined.title": "Problem activating workflow", "workflowActivator.showMessage.displayActivationError.message.catchBlock": "Sorry there was a problem requesting the error", "workflowActivator.showMessage.displayActivationError.message.errorDataNotUndefined": "The following error occurred on workflow activation:", "workflowActivator.showMessage.displayActivationError.message.errorDataUndefined": "Unknown error", "workflowActivator.showMessage.displayActivationError.title": "Problem activating workflow", "workflowActivator.theWorkflowIsSetToBeActiveBut": "The workflow is activated but could not be started.<br />Click to display error message.", "workflowActivator.thisWorkflowHasNoTriggerNodes": "This workflow has no trigger nodes that require activation", "workflowActivator.thisWorkflowIsArchived": "This workflow is archived so it cannot be activated", "workflowActivator.thisWorkflowHasOnlyOneExecuteWorkflowTriggerNode": "'Execute Workflow Trigger' doesn't require activation as it is triggered by another workflow", "workflowDetails.share": "Share", "workflowDetails.active": "Active", "workflowDetails.addTag": "Add tag", "workflowDetails.chooseOrCreateATag": "Choose or create a tag", "workflowExtraction.error.failure": "Sub-workflow conversion failed", "workflowExtraction.error.selectionGraph.inputEdgeToNonRoot": "Non-input node '{node}' has a connection from a node outside the current selection.", "workflowExtraction.error.selectionGraph.outputEdgeFromNonLeaf": "Non-output node '{node}' has a connection to a node outside the current selection.", "workflowExtraction.error.selectionGraph.multipleInputNodes": "Multiple nodes [{nodes}] have inputs from outside the selection.", "workflowExtraction.error.selectionGraph.multipleOutputNodes": "Multiple nodes [{nodes}] have outputs to outside the selection.", "workflowExtraction.error.selectionGraph.noContinuousPathFromRootToLeaf": "First node '{start}' has no selected path to last node '{end}'.", "workflowExtraction.error.selectionGraph.listHeader": "Selection is invalid because of these errors:<br><br>{body}<br><br><a href=\"https://docs.n8n.io/workflows/subworkflow-conversion/\" target=\"_blank\">See docs</a> for more info.", "workflowExtraction.error.inputNodeHasMultipleInputBranches": "First node '{node}' has multiple input branches, which sub-workflows do not support.", "workflowExtraction.error.outputNodeHasMultipleOutputBranches": "Last node '{node}' has multiple output branches, which sub-workflows do not support.", "workflowExtraction.error.triggerSelected": "Triggers cannot be converted to a sub-workflow. Please unselect {nodes}.", "workflowExtraction.error.subworkflowCreationFailed": "Sub-workflow creation failed, aborting conversion.", "workflowExtraction.success.title": "Created sub-workflow", "workflowExtraction.success.message": "<a href=\"{url}\" target=\"_blank\">Open in new Tab</a>", "workflowExtraction.modal.title": "Convert to sub-workflow", "workflowExtraction.modal.description": "Convert {nodeCount} node to a new sub-workflow | Convert {nodeCount} nodes to a new sub-workflow", "workflowHelpers.showMessage.title": "Problem saving workflow", "workflowOpen.active": "Active", "workflowOpen.couldNotLoadActiveWorkflows": "Could not load active workflows", "workflowOpen.created": "Created", "workflowOpen.filterWorkflows": "Filter by tags", "workflowOpen.name": "@:_reusableBaseText.name", "workflowOpen.openWorkflow": "Open Workflow", "workflowOpen.searchWorkflows": "Search workflows...", "workflowOpen.showError.title": "Problem loading workflows", "workflowOpen.showMessage.message": "This is the current workflow", "workflowOpen.showMessage.title": "Workflow already open", "workflowOpen.updated": "Updated", "workflowOpen.newWFButton.label": "Add workflow", "workflowOpen.newWFButton.title": "Create a new workflow", "workflowPreview.showError.arrayEmpty": "Must have an array of nodes", "workflowPreview.showError.missingWorkflow": "Missing workflow", "workflowPreview.showError.previewError.message": "Unable to preview workflow", "workflowPreview.showError.missingExecution": "Missing workflow execution", "workflowPreview.executionMode.showError.previewError.message": "Unable to preview workflow execution", "workflowPreview.showError.previewError.title": "Preview error", "workflowRun.noActiveConnectionToTheServer": "Lost connection to the server", "workflowRun.showError.deactivate": "Deactivate workflow to execute", "workflowRun.showError.productionActive": "Because of limitations in {nodeName}, n8n can't listen for test executions at the same time as listening for production ones", "workflowRun.showError.title": "Problem running workflow", "workflowRun.showError.payloadTooLarge": "Please execute the whole workflow, rather than just the node. (Existing execution data is too large.)", "workflowRun.showError.resolveOutstandingIssues": "Please resolve outstanding issues before you activate it", "workflowRun.showMessage.message": "Please fix them before executing", "workflowRun.showMessage.title": "Workflow has issues", "workflowSettings.callerIds": "IDs of workflows that can call this one", "workflowSettings.callerIds.placeholder": "e.g. 14, 18", "workflowSettings.callerPolicy": "This workflow can be called by", "workflowSettings.callerPolicy.options.any": "Any workflow", "workflowSettings.callerPolicy.options.workflowsFromPersonalProject": "Workflows created by {projectName}", "workflowSettings.callerPolicy.options.workflowsFromTeamProject": "Only workflows in {projectName}", "workflowSettings.callerPolicy.options.workflowsFromSameProject": "Only workflows in the same project", "workflowSettings.callerPolicy.options.workflowsFromAList": "Selected workflows", "workflowSettings.callerPolicy.options.none": "No other workflows", "workflowSettings.defaultTimezone": "Default - {defaultTimezoneValue}", "workflowSettings.defaultTimezoneNotValid": "Default Timezone not valid", "workflowSettings.errorWorkflow": "Error Workflow", "workflowSettings.executionOrder": "Execution Order", "workflowSettings.helpTexts.errorWorkflow": "A second workflow to run if the current one fails.<br />The second workflow should have an 'Error Trigger' node.", "workflowSettings.helpTexts.executionTimeout": "How long the workflow should wait before timing out", "workflowSettings.helpTexts.executionTimeoutToggle": "Whether to cancel workflow execution after a defined time", "workflowSettings.helpTexts.saveDataErrorExecution": "Whether to save data of executions that fail", "workflowSettings.helpTexts.saveDataSuccessExecution": "Whether to save data of executions that finish successfully", "workflowSettings.helpTexts.saveExecutionProgress": "Whether to save data after each node execution. This allows you to resume from where execution stopped if there is an error, but may increase latency.", "workflowSettings.helpTexts.saveManualExecutions": "Whether to save data of executions that are started manually from the editor", "workflowSettings.helpTexts.timezone": "The timezone in which the workflow should run. Used by 'cron' node, for example.", "workflowSettings.helpTexts.workflowCallerIds": "The IDs of the workflows that are allowed to execute this one (using an ‘execute workflow’ node). The ID can be found at the end of the workflow’s URL. Separate multiple IDs by commas.", "workflowSettings.helpTexts.workflowCallerPolicy": "Workflows that are allowed to call this workflow using the Execute Workflow node", "workflowSettings.hours": "hours", "workflowSettings.minutes": "minutes", "workflowSettings.noWorkflow": "- No Workflow -", "workflowSettings.save": "@:_reusableBaseText.save", "workflowSettings.saveDataErrorExecution": "Save failed production executions", "workflowSettings.saveDataErrorExecutionOptions.defaultSave": "Default - {defaultValue}", "workflowSettings.saveDataErrorExecutionOptions.doNotSave": "Do not save", "workflowSettings.saveDataErrorExecutionOptions.save": "@:_reusableBaseText.save", "workflowSettings.saveDataSuccessExecution": "Save successful production executions", "workflowSettings.saveDataSuccessExecutionOptions.defaultSave": "Default - {defaultValue}", "workflowSettings.saveDataSuccessExecutionOptions.doNotSave": "Do not save", "workflowSettings.saveDataSuccessExecutionOptions.save": "@:_reusableBaseText.save", "workflowSettings.saveExecutionProgress": "Save execution progress", "workflowSettings.saveExecutionProgressOptions.defaultSave": "Default - {defaultValue}", "workflowSettings.saveExecutionProgressOptions.doNotSave": "Do not save", "workflowSettings.saveExecutionProgressOptions.save": "@:_reusableBaseText.save", "workflowSettings.saveManualExecutions": "Save manual executions", "workflowSettings.saveManualOptions.defaultSave": "Default - {defaultValue}", "workflowSettings.saveManualOptions.doNotSave": "Do not save", "workflowSettings.saveManualOptions.save": "@:_reusableBaseText.save", "workflowSettings.seconds": "seconds", "workflowSettings.selectOption": "Select Option", "workflowSettings.settingsFor": "Workflow settings for {workflowName} (#{workflowId})", "workflowSettings.showError.saveSettings1.errorMessage": "Timeout is activated but set to 0", "workflowSettings.showError.saveSettings1.message": "There was a problem saving the settings", "workflowSettings.showError.saveSettings1.title": "Problem saving settings", "workflowSettings.showError.saveSettings2.errorMessage": "Maximum Timeout is: {hours} hours, {minutes} minutes, {seconds} seconds", "workflowSettings.showError.saveSettings2.message": "The timeout is longer than allowed", "workflowSettings.showError.saveSettings2.title": "Problem saving settings", "workflowSettings.showError.saveSettings3.title": "Problem saving settings", "workflowSettings.showMessage.saveSettings.title": "Workflow settings saved", "workflowSettings.timeoutAfter": "Timeout After", "workflowSettings.timeoutWorkflow": "Timeout Workflow", "workflowSettings.timezone": "Timezone", "workflowSettings.timeSavedPerExecution": "Estimated time saved", "workflowSettings.timeSavedPerExecution.hint": "Minutes per production execution", "workflowSettings.timeSavedPerExecution.tooltip": "Total time savings are summarised in the Overview page.", "workflowHistory.title": "Version History", "workflowHistory.content.title": "Version", "workflowHistory.content.editedBy": "Edited by", "workflowHistory.content.versionId": "Version ID", "workflowHistory.content.actions": "Actions", "workflowHistory.item.id": "ID: {id}", "workflowHistory.item.createdAt": "{date} at {time}", "workflowHistory.item.actions.restore": "Restore this version", "workflowHistory.item.actions.clone": "Clone to new workflow", "workflowHistory.item.actions.open": "Open version in new tab", "workflowHistory.item.actions.download": "Download", "workflowHistory.item.unsaved.title": "Unsaved version", "workflowHistory.item.latest": "Latest saved", "workflowHistory.empty": "No versions yet.", "workflowHistory.hint": "Save the workflow to create the first version!", "workflowHistory.limit": "Version history is limited to {evaluatedPruneTime} days", "workflowHistory.upgrade": "{link} to activate full history", "workflowHistory.upgrade.link": "Upgrade plan", "workflowHistory.action.error.title": "Failed to {action}", "workflowHistory.action.restore.modal.title": "Restore previous workflow version?", "workflowHistory.action.restore.modal.subtitle": "Your workflow will revert to the version from {date}", "workflowHistory.action.restore.modal.text": "Your workflow is currently active, so production executions will immediately start using the restored version. If you'd like to deactivate it before restoring, click {buttonText}.", "workflowHistory.action.restore.modal.button.deactivateAndRestore": "Deactivate and restore", "workflowHistory.action.restore.modal.button.restore": "Rest<PERSON>", "workflowHistory.action.restore.modal.button.cancel": "Cancel", "workflowHistory.action.restore.success.title": "Successfully restored workflow version", "workflowHistory.action.clone.success.title": "Successfully cloned workflow version", "workflowHistory.action.clone.success.message": "Open cloned workflow in a new tab", "workflowHistory.button.tooltip.empty": "This workflow currently has no history to view. Once you've made your first save, you'll be able to view previous versions", "workflowHistory.button.tooltip.enabled": "Workflow history to view and restore previous versions of your workflows", "workflowHistory.button.tooltip.disabled": "Upgrade to unlock workflow history to view and restore previous versions of your workflows. {link}", "workflowHistory.button.tooltip.disabled.link": "View plans", "workflows.heading": "Workflows", "workflows.add": "Add workflow", "workflows.project.add": "Add workflow to project", "workflows.item.open": "Open", "workflows.item.share": "Share...", "workflows.item.duplicate": "Duplicate", "workflows.item.delete": "Delete", "workflows.item.archive": "Archive", "workflows.item.unarchive": "Unarchive", "workflows.item.move": "Move", "workflows.item.changeOwner": "Change owner", "workflows.item.updated": "Last updated", "workflows.item.created": "Created", "workflows.item.readonly": "Read only", "workflows.item.archived": "Archived", "workflows.search.placeholder": "Search", "workflows.filters": "Filters", "workflows.filters.tags": "Tags", "workflows.filters.status": "Status", "workflows.filters.status.all": "All", "workflows.filters.status.active": "Active", "workflows.filters.status.deactivated": "Deactivated", "workflows.filters.showArchived": "Show archived workflows", "workflows.filters.ownedBy": "Owned by", "workflows.filters.sharedWith": "Shared with", "workflows.filters.apply": "Apply filters", "workflows.filters.reset": "Reset all", "workflows.filters.active": "Some workflows may be hidden since filters are applied.", "workflows.filters.active.shortText": "Filters are applied.", "workflows.filters.active.reset": "Remove filters", "workflows.sort.lastUpdated": "Sort by last updated", "workflows.sort.lastCreated": "Sort by last created", "workflows.sort.nameAsc": "Sort by name (A-Z)", "workflows.sort.nameDesc": "Sort by name (Z-A)", "workflows.noResults": "No workflows found", "workflows.noResults.withSearch.switchToShared.preamble": "some workflows may be", "workflows.noResults.withSearch.switchToShared.link": "hidden", "workflows.empty.heading": "👋 Welcome {name}!", "workflows.empty.heading.userNotSetup": "👋 Welcome!", "workflows.empty.description": "Create your first workflow", "workflows.empty.description.readOnlyEnv": "No workflows here yet", "workflows.empty.description.noPermission": "There are currently no workflows to view", "workflows.empty.startFromScratch": "Start from scratch", "workflows.empty.startWithTemplate": "Start with a template", "workflows.empty.browseTemplates": "Explore workflow templates", "workflows.empty.learnN8n": "Learn n8n", "workflows.empty.button.disabled.tooltip": "Your current role in the project does not allow you to create workflows", "workflows.empty.easyAI": "Test a simple AI Agent example", "workflows.empty.shared-with-me": "No {resource} has been shared with you", "workflows.empty.shared-with-me.link": "<a href=\"#\">Back to Personal</a>", "workflows.list.easyAI": "Test the power of AI in n8n with this simple AI Agent Workflow", "workflows.list.error.fetching": "Error fetching workflows", "workflows.shareModal.title": "Share '{name}'", "workflows.shareModal.title.static": "Shared with {projectName}", "workflows.shareModal.select.placeholder": "Add users...", "workflows.shareModal.list.delete": "Remove access", "workflows.shareModal.list.delete.confirm.title": "Remove {name}'s access?", "workflows.shareModal.list.delete.confirm.lastUserWithAccessToCredentials.message": "If you do this, the workflow will lose access to {name}’s credentials. <strong>Nodes that use those credentials will stop working</strong>.", "workflows.shareModal.list.delete.confirm.confirmButtonText": "Remove access", "workflows.shareModal.list.delete.confirm.cancelButtonText": "Cancel", "workflows.shareModal.onSave.success.title": "Sharing updated", "workflows.shareModal.onSave.error.title": "There was a problem saving sharing settings", "workflows.shareModal.saveBeforeClose.title": "Save sharing changes?", "workflows.shareModal.saveBeforeClose.message": "You have unsaved changes. Do you want to save them before closing?", "workflows.shareModal.saveBeforeClose.confirmButtonText": "Save", "workflows.shareModal.saveBeforeClose.cancelButtonText": "Close without saving", "workflows.shareModal.save": "Save", "workflows.shareModal.changesHint": "You made changes", "workflows.shareModal.info.sharee": "Only {workflowOwnerName} or users with workflow sharing permission can change who this workflow is shared with", "workflows.shareModal.info.sharee.fallback": "the owner", "workflows.shareModal.info.members": "This workflow is owned by the {projectName} project which currently has {members} with access to this workflow.", "workflows.shareModal.info.members.number": "{number} member | {number} members", "workflows.shareModal.role.editor": "Editor", "workflows.roles.editor": "Editor", "workflows.concurrentChanges.confirmMessage.title": "Workflow was changed by someone else", "workflows.concurrentChanges.confirmMessage.message": "Someone saved this workflow while you were editing it. You can <a href=\"{url}\" target=\"_blank\">view their version</a> (in new tab).<br/><br/>Overwrite their changes with yours?", "workflows.concurrentChanges.confirmMessage.cancelButtonText": "Cancel", "workflows.concurrentChanges.confirmMessage.confirmButtonText": "Overwrite and Save", "workflows.create.personal.toast.title": "Workflow successfully created", "workflows.create.personal.toast.text": "This workflow has been created inside your personal space.", "workflows.create.project.toast.title": "Workflow successfully created in {projectName}", "workflows.create.folder.toast.title": "Workflow successfully created in \"{projectName}\", within \"{folderName}\"", "workflows.create.project.toast.text": "All members from {projectName} will have access to this workflow.", "workflows.deactivated": "Workflow deactivated", "workflowSelectorParameterInput.createNewSubworkflow.name": "My Sub-Workflow", "importCurlModal.title": "Import cURL command", "importCurlModal.input.label": "cURL Command", "importCurlModal.input.placeholder": "Paste the cURL command here", "ImportCurlModal.notice.content": "This will overwrite any changes you have already made to the current node", "importCurlModal.button.label": "Import", "importCurlParameter.label": "Import cURL", "importCurlParameter.showError.invalidCurlCommand.title": "Couldn’t import cURL command", "importCurlParameter.showError.invalidCurlCommand.message": "This command is in an unsupported format", "importCurlParameter.showError.invalidProtocol1.title": "Use the {node} node", "importCurlParameter.showError.invalidProtocol2.title": "Invalid Protocol", "importCurlParameter.showError.invalidProtocol.message": "The HTTP node doesn’t support {protocol} requests", "variables.heading": "Variables", "variables.add": "Add variable", "variables.add.unavailable": "Upgrade plan to keep using variables", "variables.add.unavailable.empty": "Upgrade plan to start using variables", "variables.add.onlyOwnerCanCreate": "Only owner can create variables", "variables.empty.heading": "{name}, let's set up a variable", "variables.empty.heading.userNotSetup": "Set up a variable", "variables.empty.description": "Variables can be used to store data that can be referenced easily across multiple workflows.", "variables.empty.button": "Add first variable", "variables.empty.button.disabled.tooltip": "Your current role in the project does not allow you to create variables", "variables.empty.notAllowedToCreate.heading": "{name}, start using variables", "variables.empty.notAllowedToCreate.description": "Ask your n8n instance owner to create the variables you need. Once configured, you can utilize them in your workflows using the syntax $vars.MY_VAR.", "variables.filters.active": "Some variables may be hidden since filters are applied.", "variables.filters.active.reset": "Remove filters", "variables.noResults": "No variables found", "variables.sort.nameAsc": "Sort by name (A-Z)", "variables.sort.nameDesc": "Sort by name (Z-A)", "variables.table.key": "Key", "variables.table.value": "Value", "variables.table.usage": "Usage Syntax", "variables.editing.key.placeholder": "Enter a name", "variables.editing.value.placeholder": "Enter a value", "variables.editing.key.error.startsWithLetter": "This field may only start with a letter", "variables.editing.key.error.jsonKey": "This field may contain only letters, numbers, and underscores", "variables.row.button.save": "Save", "variables.row.button.cancel": "Cancel", "variables.row.button.edit": "Edit", "variables.row.button.edit.onlyRoleCanEdit": "Only instance owner and admins can edit variables", "variables.row.button.delete": "Delete", "variables.row.button.delete.onlyRoleCanDelete": "Only instance owner and can delete variables", "variables.row.usage.copiedToClipboard": "Copied to clipboard", "variables.row.usage.copyToClipboard": "Copy to clipboard", "variables.search.placeholder": "Search variables...", "variables.errors.save": "Error while saving variable", "variables.errors.delete": "Error while deleting variable", "variables.modals.deleteConfirm.title": "Delete variable", "variables.modals.deleteConfirm.message": "Are you sure you want to delete the variable \"{name}\"? This cannot be undone.", "variables.modals.deleteConfirm.confirmButton": "Delete", "variables.modals.deleteConfirm.cancelButton": "Cancel", "contextual.credentials.sharing.unavailable.title": "Upgrade to collaborate", "contextual.credentials.sharing.unavailable.title.cloud": "Upgrade to collaborate", "contextual.credentials.sharing.unavailable.description": "You can share credentials with others when you upgrade your plan.", "contextual.credentials.sharing.unavailable.description.cloud": "You can share credentials with others when you upgrade your plan.", "contextual.credentials.sharing.unavailable.button": "View plans", "contextual.credentials.sharing.unavailable.button.cloud": "Upgrade now", "contextual.workflows.sharing.title": "Sharing", "contextual.workflows.sharing.unavailable.title": "Sharing", "contextual.workflows.sharing.unavailable.title.cloud": "Upgrade to collaborate", "contextual.workflows.sharing.unavailable.description.modal": "You can collaborate with others on workflows when you upgrade your plan.", "contextual.workflows.sharing.unavailable.description.modal.cloud": "You can collaborate with others on workflows when you upgrade your plan.", "contextual.workflows.sharing.unavailable.description.tooltip": "You can collaborate with others on workflows when you upgrade your plan. {action}", "contextual.workflows.sharing.unavailable.description.tooltip.cloud": "You can collaborate with others on workflows when you upgrade your plan. {action}", "contextual.workflows.sharing.unavailable.button": "View plans", "contextual.workflows.sharing.unavailable.button.cloud": "Upgrade now", "contextual.variables.unavailable.title": "Available on the Enterprise plan", "contextual.variables.unavailable.title.cloud": "Available on Pro plan", "contextual.variables.unavailable.description": "Variables can be used to store and access data across workflows. Reference them in n8n using the prefix <code>$vars</code> (e.g. <code>$vars.myVariable</code>). Variables are immutable and cannot be modified within your workflows.<br/><a href=\"https://docs.n8n.io/environments/variables/\" target=\"_blank\">Learn more in the docs.</a>", "contextual.variables.unavailable.button": "View plans", "contextual.variables.unavailable.button.cloud": "Upgrade now", "contextual.users.settings.unavailable.title": "Upgrade to add users", "contextual.users.settings.unavailable.title.cloud": "Upgrade to add users", "contextual.users.settings.unavailable.description": "Create multiple users on our higher plans and share workflows and credentials to collaborate", "contextual.users.settings.unavailable.description.cloud": "Create multiple users on our higher plans and share workflows and credentials to collaborate", "contextual.users.settings.unavailable.button": "View plans", "contextual.users.settings.unavailable.button.cloud": "Upgrade now", "contextual.feature.unavailable.title": "Available on the Enterprise Plan", "contextual.feature.unavailable.title.cloud": "Available on the Pro Plan", "settings.ldap": "LDAP", "settings.ldap.note": "LDAP allows users to authenticate with their centralized account. It's compatible with services that provide an LDAP interface like Active Directory, Okta and Jumpcloud.", "settings.ldap.infoTip": "Learn more about <a href='https://docs.n8n.io/user-management/ldap/' target='_blank'>LDAP in the Docs</a>", "settings.ldap.save": "Save connection", "settings.ldap.connectionTestError": "Problem testing LDAP connection", "settings.ldap.connectionTest": "LDAP connection tested", "settings.ldap.runSync.title": "LDAP synchronization done", "settings.ldap.runSync.showError.message": "Problem during synchronization. Check the logs", "settings.ldap.updateConfiguration": "LDAP configuration updated", "settings.ldap.testingConnection": "Testing connection", "settings.ldap.testConnection": "Test connection", "settings.ldap.synchronizationTable.column.status": "Status", "settings.ldap.synchronizationTable.column.endedAt": "Ended At", "settings.ldap.synchronizationTable.column.runMode": "Run Mode", "settings.ldap.synchronizationTable.column.runTime": "Run Time", "settings.ldap.synchronizationTable.column.details": "Details", "settings.ldap.synchronizationTable.empty.message": "Test synchronization to preview updates", "settings.ldap.dryRun": "Test synchronization", "settings.ldap.synchronizeNow": "Run synchronization", "settings.ldap.synchronizationError": "LDAP Synchronization Error", "settings.ldap.configurationError": "LDAP Configuration Error", "settings.ldap.usersScanned": "Users scanned {scanned}", "settings.ldap.confirmMessage.beforeSaveForm.cancelButtonText": "No", "settings.ldap.confirmMessage.beforeSaveForm.confirmButtonText": "Yes, disable it", "settings.ldap.confirmMessage.beforeSaveForm.headline": "Are you sure you want to disable LDAP login?", "settings.ldap.confirmMessage.beforeSaveForm.message": "If you do so, all LDAP users will be converted to email users.", "settings.ldap.disabled.title": "Available on the Enterprise plan", "settings.ldap.disabled.description": "LDAP is available as a paid feature. Learn more about it.", "settings.ldap.disabled.buttonText": "See plans", "settings.ldap.toast.sync.success": "Synchronization succeeded", "settings.ldap.toast.connection.success": "Connection succeeded", "settings.ldap.form.loginEnabled.label": "Enable LDAP Login", "settings.ldap.form.loginEnabled.tooltip": "Connection settings and data will still be saved if you disable LDAP Login", "settings.ldap.form.loginLabel.label": "LDAP Login", "settings.ldap.form.loginLabel.placeholder": "e.g. LDAP Username or email address", "settings.ldap.form.loginLabel.infoText": "The placeholder text that appears in the login field on the login page", "settings.ldap.form.serverAddress.label": "LDAP Server Address", "settings.ldap.form.serverAddress.placeholder": "***************", "settings.ldap.form.serverAddress.infoText": "IP or domain of the LDAP server", "settings.ldap.form.port.label": "LDAP Server Port", "settings.ldap.form.port.infoText": "Port used to connect to the LDAP server", "settings.ldap.form.connectionSecurity.label": "Connection Security", "settings.ldap.form.connectionSecurity.infoText": "Type of connection security", "settings.ldap.form.allowUnauthorizedCerts.label": "Ignore SSL/TLS Issues", "settings.ldap.form.baseDn.label": "Base DN", "settings.ldap.form.baseDn.placeholder": "o=acme,dc=example,dc=com", "settings.ldap.form.baseDn.infoText": "Distinguished Name of the location where n8n should start its search for user in the AD/LDAP tree", "settings.ldap.form.bindingType.label": "Binding as", "settings.ldap.form.bindingType.infoText": "Type of binding used to connection to the LDAP server", "settings.ldap.form.adminDn.label": "Binding DN", "settings.ldap.form.adminDn.placeholder": "uid=2da2de69435c,ou=Users,o=Acme,dc=com", "settings.ldap.form.adminDn.infoText": "Distinguished Name of the user to perform the search", "settings.ldap.form.adminPassword.label": "Binding Password", "settings.ldap.form.adminPassword.infoText": "Password of the user provided in the Binding DN field above", "settings.ldap.form.userFilter.label": "User Filter", "settings.ldap.form.userFilter.placeholder": "(ObjectClass=user)", "settings.ldap.form.userFilter.infoText": "LDAP query to use when searching for user. Only users returned by this filter will be allowed to sign-in in n8n", "settings.ldap.form.attributeMappingInfo.label": "Attribute mapping", "settings.ldap.form.ldapId.label": "ID", "settings.ldap.form.ldapId.placeholder": "uid", "settings.ldap.form.ldapId.infoText": "The attribute in the LDAP server used as a unique identifier in n8n. It should be an unique LDAP attribute like uid", "settings.ldap.form.loginId.label": "Login ID", "settings.ldap.form.loginId.placeholder": "mail", "settings.ldap.form.loginId.infoText": "The attribute in the LDAP server used to log-in in n8n", "settings.ldap.form.email.label": "Email", "settings.ldap.form.email.placeholder": "mail", "settings.ldap.form.email.infoText": "The attribute in the LDAP server used to populate the email in n8n", "settings.ldap.form.firstName.label": "First Name", "settings.ldap.form.firstName.placeholder": "<PERSON><PERSON><PERSON>", "settings.ldap.form.firstName.infoText": "The attribute in the LDAP server used to populate the first name in n8n", "settings.ldap.form.lastName.label": "Last Name", "settings.ldap.form.lastName.placeholder": "sn", "settings.ldap.form.lastName.infoText": "The attribute in the LDAP server used to populate the last name in n8n", "settings.ldap.form.synchronizationEnabled.label": "Enable periodic LDAP synchronization", "settings.ldap.form.synchronizationEnabled.tooltip": "Enable users to be synchronized periodically", "settings.ldap.form.synchronizationInterval.label": "Synchronization Interval (Minutes)", "settings.ldap.form.synchronizationInterval.infoText": "How often the synchronization should run", "settings.ldap.form.pageSize.label": "<PERSON>", "settings.ldap.form.pageSize.infoText": "Max number of records to return per page during synchronization. 0 for unlimited", "settings.ldap.form.searchTimeout.label": "Search Timeout (Seconds)", "settings.ldap.form.searchTimeout.infoText": "The timeout value for queries to the AD/LDAP server. Increase if you are getting timeout errors caused by a slow AD/LDAP server", "settings.ldap.section.synchronization.title": "Synchronization", "settings.sso": "SSO", "settings.sso.title": "Single Sign On", "settings.sso.subtitle": "SAML 2.0 Configuration", "settings.sso.info": "Activate SAML or OIDC to enable passwordless login via your existing user management tool and enhance security through unified authentication.", "settings.sso.info.link": "Learn how to configure SAML or OIDC.", "settings.sso.activation.tooltip": "You need to save the settings first before activating SAML", "settings.sso.activated": "Activated", "settings.sso.deactivated": "Deactivated", "settings.sso.settings.redirectUrl.label": "Redirect URL", "settings.sso.settings.redirectUrl.copied": "Redirect URL copied to clipboard", "settings.sso.settings.redirectUrl.help": "Copy the Redirect URL to configure your SAML provider", "settings.sso.settings.entityId.label": "Entity ID", "settings.sso.settings.entityId.copied": "Entity ID copied to clipboard", "settings.sso.settings.entityId.help": "Copy the Entity ID URL to configure your SAML provider", "settings.sso.settings.ips.label": "Identity Provider Settings", "settings.sso.settings.ips.xml.help": "Paste here the raw Metadata XML provided by your Identity Provider", "settings.sso.settings.ips.url.help": "Paste here the Identity Provider Metadata URL", "settings.sso.settings.ips.url.placeholder": "e.g. https://samltest.id/saml/idp", "settings.sso.settings.ips.url.invalid": "The Identity Provider Metadata URL is not valid", "settings.sso.settings.ips.options.url": "Metadata URL", "settings.sso.settings.ips.options.xml": "XML", "settings.sso.settings.test": "Test settings", "settings.sso.settings.save": "Save settings", "settings.sso.settings.save.activate.title": "Test and activate SAML SSO", "settings.sso.settings.save.activate.message": "SAML SSO configuration saved successfully. Test your SAML SSO settings first, then activate to enable single sign-on for your organization.", "settings.sso.settings.save.activate.cancel": "Cancel", "settings.sso.settings.save.activate.test": "Test settings", "settings.sso.settings.save.error": "Error saving SAML SSO configuration", "settings.sso.settings.footer.hint": "Don't forget to activate SAML SSO once you've saved the settings.", "settings.sso.actionBox.title": "Available on the Enterprise plan", "settings.sso.actionBox.description": "Use Single Sign On to consolidate authentication into a single platform to improve security and agility.", "settings.sso.actionBox.buttonText": "See plans", "settings.oidc.confirmMessage.beforeSaveForm.headline": "Are you sure you want to disable OIDC login?", "settings.oidc.confirmMessage.beforeSaveForm.message": "If you do so, all OIDC users will be converted to email users.", "settings.mfa.secret": "Secret {secret}", "settings.mfa": "MFA", "settings.mfa.title": "Multi-factor Authentication", "settings.mfa.updateConfiguration": "MFA configuration updated", "settings.mfa.invalidAuthenticatorCode": "Invalid authenticator code", "projects.header.overview.subtitle": "All the workflows, credentials and executions you have access to", "projects.header.shared.title": "Shared with you", "projects.header.personal.subtitle": "Workflows and credentials owned by you", "projects.header.shared.subtitle": "Workflows and credentials other users have shared with you", "projects.header.create.workflow": "Create Workflow", "projects.header.create.credential": "Create Credential", "projects.header.create.folder": "Create Folder", "projects.create": "Create", "projects.create.personal": "Create in personal", "projects.create.team": "Create in project", "projects.menu.overview": "Overview", "projects.menu.shared": "Shared with you", "projects.menu.title": "Projects", "projects.menu.personal": "Personal", "projects.menu.addFirstProject": "Add project", "projects.settings": "Project settings", "projects.settings.newProjectName": "My project", "projects.settings.iconPicker.button.tooltip": "Choose project icon", "projects.settings.name": "Project icon and name", "projects.settings.description": "Project description", "projects.settings.projectMembers": "Project members", "projects.settings.message.unsavedChanges": "You have unsaved changes", "projects.settings.danger.message": "When deleting a project, you can also choose to move all workflows and credentials to another project.", "projects.settings.danger.title": "Danger zone", "projects.settings.danger.deleteProject": "Delete this project", "projects.settings.button.save": "@:_reusableBaseText.save", "projects.settings.button.cancel": "@:_reusableBaseText.cancel", "projects.settings.button.deleteProject": "Delete project", "projects.settings.role.admin": "Admin", "projects.settings.role.editor": "Editor", "projects.settings.role.viewer": "Viewer", "projects.settings.delete.title": "Delete \"{projectName}\" Project?", "projects.settings.delete.message": "What should we do with the project data?", "projects.settings.delete.message.empty": "There are no workflows or credentials in this project.", "projects.settings.delete.question.transfer.label": "Transfer its workflows and credentials to another project or user", "projects.settings.delete.question.transfer.title": "Project or user to transfer to", "projects.settings.delete.question.wipe.label": "Delete its workflows and credentials", "projects.settings.delete.question.wipe.title": "Type \"delete all data\" to confirm", "projects.settings.delete.question.wipe.placeholder": "delete all data", "projects.settings.delete.confirm": "Yes, I am sure", "projects.settings.delete.cancel": "No, cancel", "projects.settings.delete.successful.title": "Project {projectName} deleted", "projects.settings.delete.error.title": "An error occurred while deleting the project", "projects.settings.save.successful.title": "Project {projectName} saved successfully", "projects.settings.icon.update.successful.title": "Project icon updated successfully", "projects.settings.save.error.title": "An error occurred while saving the project", "projects.settings.role.upgrade.title": "Upgrade to unlock additional roles", "projects.settings.role.upgrade.message": "You're currently limited to {limit} on the {planName} plan and can only assign the admin role to users within this project. To create more projects and unlock additional roles, upgrade your plan.", "projects.sharing.noMatchingProjects": "There are no available projects", "projects.sharing.noMatchingUsers": "No matching users or projects", "projects.sharing.select.placeholder": "Select project or user", "projects.sharing.select.placeholder.user": "Share with user(s)", "projects.sharing.select.placeholder.project": "Share with projects or users", "projects.error.title": "Project error", "projects.create.limit": "{count} project | {count} projects", "projects.create.limitReached": "You have reached the {planName} plan limit of {limit}. Upgrade your plan to unlock more projects. {link}", "projects.create.limitReached.cloud": "You have reached the {planName} plan limit of {limit}. Upgrade your plan to unlock more projects.", "projects.create.limitReached.self": "Upgrade to unlock projects for more granular control over sharing, access and organisation of workflows", "projects.create.limitReached.link": "View plans", "projects.create.permissionDenied": "Your current role does not allow you to create projects", "projects.move.resource.modal.title": "Choose a project or user to move this {resourceTypeLabel} to", "projects.move.resource.modal.message": "\"{resourceName}\" is currently {inPersonalProject}{inTeamProject}", "projects.move.resource.modal.message.team": "in the \"{resourceHomeProjectName}\" project.", "projects.move.resource.modal.message.personal": "owned by \"{resourceHomeProjectName}\".", "projects.move.resource.modal.message.note": "Note", "projects.move.resource.modal.message.sharingNote": "{note}: Moving will remove any existing sharing for this {resourceTypeLabel}.", "projects.move.resource.modal.message.sharingInfo": "(Currently shared with {count} project) | (Currently shared with {count} projects)", "projects.move.resource.modal.message.usedCredentials": "Also share the {usedCredentials} used by this workflow to ensure it will continue to run correctly", "projects.move.resource.modal.message.usedCredentials.number": "{count} credential | {count} credentials", "projects.move.resource.modal.message.unAccessibleCredentials": "Some credentials", "projects.move.resource.modal.message.unAccessibleCredentials.note": "{credentials} used in this workflow will not be shared", "projects.move.resource.modal.message.noProjects": "Currently there are not any projects or users available for you to move this {resourceTypeLabel} to.", "projects.move.resource.modal.button": "Move {resourceTypeLabel}", "projects.move.resource.modal.selectPlaceholder": "Select project or user...", "projects.move.resource.error.title": "Error moving {resourceName} {resourceTypeLabel}", "projects.move.resource.success.title": "{resourceTypeLabel} '{resourceName}' is moved to '{targetProjectName}'", "projects.move.resource.success.message.workflow": "The workflow's credentials were not shared with the project.", "projects.move.resource.success.message.workflow.withAllCredentials": "The workflow's credentials were shared with the project.", "projects.move.resource.success.message.workflow.withSomeCredentials": "Due to missing permissions not all the workflow's credentials were shared with the project.", "projects.move.resource.success.link": "View {targetProjectName}", "projects.badge.tooltip.sharedOwned": "This {resourceTypeLabel} is owned by you and shared with {count} users", "projects.badge.tooltip.sharedPersonal": "This {resourceTypeLabel} is owned by {name} and shared with {count} users", "projects.badge.tooltip.personal": "This {resourceTypeLabel} is owned by {name}", "projects.badge.tooltip.team": "This {resourceTypeLabel} is owned and accessible by the {name} project.", "projects.badge.tooltip.sharedTeam": "This {resourceTypeLabel} is owned by the {name} project and accessible by {count} users", "mfa.setup.invalidAuthenticatorCode": "{code} is not a valid number", "mfa.setup.invalidCode": "Two-factor code failed. Please try again.", "mfa.code.modal.title": "Two-factor authentication", "mfa.recovery.modal.title": "Two-factor recovery", "mfa.code.input.info": "Don't have your auth device?", "mfa.code.input.info.action": "Enter a recovery code", "mfa.recovery.input.info.action": "enter a recovery code", "mfa.code.button.continue": "Continue", "mfa.recovery.button.verify": "Verify", "mfa.button.back": "Back", "mfa.code.input.label": "Two-factor code", "mfa.code.input.placeholder": "e.g. 123456", "mfa.code.recovery.input.label": "Two-factor code or recovery code", "mfa.code.recovery.input.placeholder": "e.g. 123456 or c79f9c02-7b2e-44...", "mfa.recovery.input.label": "Recovery code", "mfa.recovery.input.placeholder": "e.g c79f9c02-7b2e-44...", "mfa.code.invalid": "This code is invalid, try again or", "mfa.recovery.invalid": "This code is invalid or was already used, try again", "mfa.setup.step1.title": "Setup Authenticator app [1/2]", "mfa.setup.step2.title": "Download your recovery codes [2/2]", "mfa.setup.step1.instruction1.title": "1. <PERSON><PERSON> the QR code", "mfa.setup.step1.instruction1.subtitle": "{part1} {part2}", "mfa.setup.step1.instruction1.subtitle.part1": "Use an authenticator app from your phone to scan. If you can't scan the QR code, enter", "mfa.setup.step1.instruction1.subtitle.part2": "this text code", "mfa.setup.step1.instruction2.title": "2. Enter the code from the app", "mfa.setup.step2.description": "You can use recovery codes as a second factor to authenticate in case you lose access to your device.", "mfa.setup.step2.infobox.description": "{part1} {part2}", "mfa.setup.step2.infobox.description.part1": "Keep your recovery codes somewhere safe. If you lose your device and your recovery codes, you will", "mfa.setup.step2.infobox.description.part2": "lose access to your account.", "mfa.setup.step2.button.download": "Download recovery codes", "mfa.setup.step2.button.save": "I have downloaded my recovery codes", "mfa.setup.step1.button.continue": "Continue", "mfa.setup.step1.input.label": "Code from your authenticator app", "mfa.setup.step1.toast.copyToClipboard.title": "Code copied to clipboard", "mfa.setup.step1.toast.copyToClipboard.message": "Enter the code in your authenticator app", "mfa.setup.step2.toast.setupFinished.message": "Two-factor authentication enabled", "mfa.setup.step2.toast.setupFinished.error.message": "Error enabling two-factor authentication", "mfa.setup.step2.toast.tokenExpired.error.message": "MFA token expired. Close the modal and enable MFA again", "mfa.prompt.code.modal.title": "Two-factor code or recovery code required", "settings.personal.mfa.section.title": "Two-factor authentication (2FA)", "settings.personal.personalisation": "Personalisation", "settings.personal.theme": "Theme", "settings.personal.theme.systemDefault": "System default", "settings.personal.theme.light": "Light theme", "settings.personal.theme.dark": "Dark theme", "settings.personal.mfa.button.disabled.infobox": "Two-factor authentication is currently disabled.", "settings.personal.mfa.button.enabled.infobox": "Two-factor authentication is currently enabled.", "settings.personal.mfa.button.enabled": "Enable 2FA", "settings.personal.mfa.button.disabled": "Disable two-factor authentication", "settings.personal.mfa.toast.disabledMfa.title": "Two-factor authentication disabled", "settings.personal.mfa.toast.disabledMfa.message": "You will no longer need your authenticator app when signing in", "settings.personal.mfa.toast.disabledMfa.error.message": "Error disabling two-factor authentication", "settings.personal.mfa.toast.canEnableMfa.title": "MFA pre-requisite failed", "settings.personal.mfa.enforced": "The settings on this instance <strong>require you to set up 2FA</strong>. Please enable it to continue working in this instance.", "settings.personal.mfa.enforce.message": "Enforces 2FA for all users on this instance.", "settings.personal.mfa.enforce.unlicensed_tooltip": "You can enforce 2FA for all users on this instance when you upgrade your plan. {action}", "settings.personal.mfa.enforce.unlicensed_tooltip.link": "View plans", "settings.personal.mfa.enforce.title": "Enforce two-factor authentication", "settings.personal.mfa.enforce.error": "Cannot enforce 2FA for all users", "settings.personal.mfa.enforce.enabled.title": "2FA Enforced", "settings.personal.mfa.enforce.enabled.message": "Two-factor authentication is now required for all users on this instance.", "settings.personal.mfa.enforce.disabled.title": "2FA No Longer Enforced", "settings.personal.mfa.enforce.disabled.message": "Two-factor authentication is no longer mandatory for users on this instance.", "settings.mfa.toast.noRecoveryCodeLeft.title": "No 2FA recovery codes remaining", "settings.mfa.toast.noRecoveryCodeLeft.message": "You have used all of your recovery codes. Disable then re-enable two-factor authentication to generate new codes. <a href='/settings/personal' target='_blank' >Open settings</a>", "sso.login.divider": "or", "sso.login.button": "Continue with SSO", "executionUsage.currentUsage": "{text} {count}", "executionUsage.currentUsage.text": "You are in a free trial with limited executions. You have", "executionUsage.currentUsage.count": "{n} day left. | {n} days left.", "executionUsage.label.executions": "Executions", "executionUsage.button.upgrade": "Upgrade plan", "executionUsage.expired.text": "Your trial is over. Upgrade now to keep your data.", "executionUsage.ranOutOfExecutions.text": "You’re out of executions. Upgrade your plan to keep automating.", "openExecution.missingExeuctionId.title": "Could not find execution", "openExecution.missingExeuctionId.message": "Make sure this workflow saves executions via the settings", "type.string": "String", "type.number": "Number", "type.dateTime": "Date & Time", "type.boolean": "Boolean", "type.array": "Array", "type.object": "Object", "filter.operator.equals": "is equal to", "filter.operator.notEquals": "is not equal to", "filter.operator.contains": "contains", "filter.operator.notContains": "does not contain", "filter.operator.startsWith": "starts with", "filter.operator.notStartsWith": "does not start with", "filter.operator.endsWith": "ends with", "filter.operator.notEndsWith": "does not end with", "filter.operator.exists": "exists", "filter.operator.notExists": "does not exist", "filter.operator.regex": "matches regex", "filter.operator.notRegex": "does not match regex", "filter.operator.gt": "is greater than", "filter.operator.lt": "is less than", "filter.operator.gte": "is greater than or equal to", "filter.operator.lte": "is less than or equal to", "filter.operator.after": "is after", "filter.operator.before": "is before", "filter.operator.afterOrEquals": "is after or equal to", "filter.operator.beforeOrEquals": "is before or equal to", "filter.operator.true": "is true", "filter.operator.false": "is false", "filter.operator.lengthEquals": "length equal to", "filter.operator.lengthNotEquals": "length not equal to", "filter.operator.lengthGt": "length greater than", "filter.operator.lengthLt": "length less than", "filter.operator.lengthGte": "length greater than or equal to", "filter.operator.lengthLte": "length less than or equal to", "filter.operator.empty": "is empty", "filter.operator.notEmpty": "is not empty", "filter.combinator.or": "OR", "filter.combinator.and": "AND", "filter.addCondition": "Add condition", "filter.removeCondition": "Remove condition", "filter.dragCondition": "Drag condition", "filter.maxConditions": "Maximum conditions reached", "filter.condition.resolvedTrue": "This condition is true for the first input item", "filter.condition.resolvedFalse": "This condition is false for the first input item", "filter.condition.placeholderLeft": "value1", "filter.condition.placeholderRight": "value2", "assignment.dragFields": "Drag input fields here", "assignment.dropField": "Drop here to add the field", "assignment.or": "or", "assignment.add": "Add Field", "assignment.addAll": "Add All", "assignment.clearAll": "Clear All", "templateSetup.title": "Set up '{name}' template", "templateSetup.instructions": "You need {0} account to setup this template", "templateSetup.skip": "<PERSON><PERSON>", "templateSetup.continue.button": "Continue", "templateSetup.credential.description": "The credential you select will be used in the {0} node of the workflow template. | The credential you select will be used in the {0} nodes of the workflow template.", "templateSetup.continue.button.fillRemaining": "Fill remaining credentials to continue", "setupCredentialsModal.title": "Set up template", "becomeCreator.text": "Share your workflows with 40k+ users, unlock perks, and shine as a featured template creator!", "becomeCreator.buttonText": "Become a creator", "becomeCreator.closeButtonTitle": "Close", "feedback.title": "Was this helpful?", "feedback.positive": "I found this helpful", "feedback.negative": "I didn't find this helpful", "communityPlusModal.badge": "Time limited offer", "communityPlusModal.title": "Get paid features for free (forever)", "communityPlusModal.error.title": "License request failed", "communityPlusModal.success.title": "Request sent", "communityPlusModal.success.message": "License key will be sent to {email}", "communityPlusModal.description": "Receive a free activation key for the advanced features below - lifetime access.", "communityPlusModal.features.first.title": "Workflow history", "communityPlusModal.features.first.description": "Review and restore any workflow version from the last 24 hours", "communityPlusModal.features.second.title": "Advanced debugging", "communityPlusModal.features.second.description": "Easily fix any workflow execution that’s errored, then re-run it", "communityPlusModal.features.third.title": "Execution search and tagging", "communityPlusModal.features.third.description": "Search and organize past workflow executions for easier review", "communityPlusModal.features.fourth.title": "Folders", "communityPlusModal.features.fourth.description": "Organize your workflows in a nested folder structure", "communityPlusModal.input.email.label": "Enter email to receive your license key", "communityPlusModal.button.skip": "<PERSON><PERSON>", "communityPlusModal.button.confirm": "Send me a free license key", "communityPlusModal.notice": "Included features may change, but once unlocked, you'll keep them forever.", "executeWorkflowTrigger.createNewSubworkflow": "Create a Sub-Workflow in {projectName}", "executeWorkflowTrigger.createNewSubworkflow.noProject": "Create a New Sub-Workflow", "evaluation.listRuns.status.new": "New", "evaluation.listRuns.status.running": "Running", "evaluation.listRuns.status.evaluating": "Evaluating", "evaluation.listRuns.status.completed": "Completed", "evaluation.listRuns.status.cancelled": "Cancelled", "evaluation.listRuns.status.error": "Error", "evaluation.listRuns.status.success": "Success", "evaluation.listRuns.status.warning": "Warning", "evaluation.listRuns.metricsOverTime": "Metrics over time", "evaluation.listRuns.status": "Status", "evaluation.listRuns.runListHeader": "All runs", "evaluation.listRuns.testCasesListHeader": "Run #{index}", "evaluation.listRuns.runNumber": "Run", "evaluation.listRuns.runDate": "Run date", "evaluation.listRuns.runStatus": "Run status", "evaluation.listRuns.noRuns": "No test runs", "evaluation.listRuns.pastRuns.total": "No runs | All runs | All runs", "evaluation.listRuns.noRuns.description": "Run a test to see the results here", "evaluation.listRuns.deleteRuns": "No runs to delete | Delete {count} run | Delete {count} runs", "evaluation.listRuns.noRuns.button": "Run Test", "evaluation.listRuns.toast.error.fetchTestCases": "Failed to load run details", "evaluation.listRuns.error.testCasesNotFound": "No matching rows in dataset{description}", "evaluation.listRuns.error.testCasesNotFound.description": "Check any filters or limits set in the evaluation trigger", "evaluation.listRuns.error.executionInterrupted": "Test run was interrupted", "evaluation.listRuns.error.unknownError": "Execution error{description}", "evaluation.listRuns.error.cantFetchTestRuns": "Couldn’t fetch test runs", "evaluation.listRuns.error.cantStartTestRun": "Couldn’t start test run", "evaluation.listRuns.error.unknownError.description": "Click for more details", "evaluation.listRuns.error.evaluationTriggerNotFound": "Evaluation trigger missing", "evaluation.listRuns.error.evaluationTriggerNotConfigured": "Evaluation trigger is not configured", "evaluation.listRuns.error.evaluationTriggerDisabled": "Evaluation trigger is disabled", "evaluation.listRuns.error.setOutputsNodeNotConfigured": "'Set outputs' node is not configured", "evaluation.listRuns.error.setMetricsNodeNotFound": "No 'Set metrics' node in workflow", "evaluation.listRuns.error.setMetricsNodeNotConfigured": "'Set metrics' node is not configured", "evaluation.listRuns.error.cantFetchTestCases": "Couldn’t fetch test cases{description}", "evaluation.listRuns.error.cantFetchTestCases.description": "Check the Google Sheet setup in the evaluation trigger", "evaluation.runDetail.ranAt": "Ran at", "evaluation.runDetail.testCase": "Test case", "evaluation.runDetail.testCase.id": "Test case ID", "evaluation.runDetail.testCase.status": "Test case status", "evaluation.runDetail.totalCases": "Total cases", "evaluation.runDetail.error.mockedNodeMissing": "Output for a mocked node does not exist in benchmark execution.{link}.", "evaluation.runDetail.error.mockedNodeMissing.solution": "Fix test configuration", "evaluation.runDetail.error.executionFailed": "Failed to execute workflow", "evaluation.runDetail.error.executionFailed.solution": "View execution", "evaluation.runDetail.error.datasetTriggerNotFound": "Dataset trigger does not exist in the workflow.{link}.", "evaluation.runDetail.error.datasetTriggerNotFound.solution": "View workflow", "evaluation.runDetail.error.invalidMetrics": "Evaluation metrics node returned invalid metrics. Only numeric values are expected. View workflow. {link}.", "evaluation.runDetail.error.invalidMetrics.solution": "View workflow", "evaluation.runDetail.error.unknownError": "An unknown error occurred", "evaluation.runDetail.error.unknownError.solution": "View execution", "evaluation.runDetail.error.noMetricsCollected": "No 'Set metrics' node executed", "evaluation.runDetail.error.partialCasesFailed": "Finished with errors", "evaluation.runTest": "Run Test", "evaluation.cancelTestRun": "Cancel Test Run", "evaluation.notImplemented": "This feature is not implemented yet!", "evaluation.viewDetails": "View Details", "evaluation.editTest": "Edit Test", "evaluation.deleteTest": "Delete Test", "evaluation.deleteTest.warning": "The test and all associated runs will be removed. This cannot be undone", "evaluation.testIsRunning": "Test is running. Please wait for it to finish.", "evaluation.completeConfig": "Complete the configuration below to run the test:", "evaluation.configError.noEvaluationTag": "No evaluation tag set", "evaluation.configError.noExecutionsAddedToTag": "No executions added to this tag", "evaluation.configError.noEvaluationWorkflow": "No evaluation workflow set", "evaluation.configError.noMetrics": "No metrics set", "evaluation.workflowInput.subworkflowName": "Evaluation workflow for {name}", "evaluation.workflowInput.subworkflowName.default": "My Evaluation Sub-Workflow", "evaluation.executions.addTo": "Add to Test", "evaluation.executions.addTo.new": "Add to Test", "evaluation.executions.addTo.existing": "Add to \"{name}\"", "evaluation.executions.addedTo": "Added to \"{name}\"", "evaluation.executions.removeFrom": "Remove from \"{name}\"", "evaluation.executions.removedFrom": "Execution removed from \"{name}\"", "evaluation.executions.toast.addedTo": "Go back to \"{name}\"", "evaluation.executions.tooltip.addTo": "Add to new test", "evaluation.executions.tooltip.noExecutions": "Evaluation executions can not be added to tests", "evaluation.executions.tooltip.onlySuccess": "Only successful executions can be added to tests", "evaluation.workflow.createNew": "Create new evaluation workflow", "evaluation.workflow.createNew.or": "or use existing evaluation sub-workflow", "evaluation.executions.toast.addedTo.title": "Execution added to test ", "evaluation.executions.toast.closeTab": "Close this tab", "evaluation.executions.toast.removedFrom.title": "Execution removed from test ", "evaluations.paywall.title": "Register to enable evaluation", "evaluations.paywall.description": "Register your Community instance to unlock the evaluation feature", "evaluations.paywall.cta": "Register instance", "evaluations.setupWizard.title": "Test your AI workflow over multiple inputs", "evaluations.setupWizard.description": "Evaluations measure performance against a test dataset.", "evaluations.setupWizard.moreInfo": "More info", "evaluations.setupWizard.stepHeader.optional": "Optional", "evaluations.setupWizard.step1.title": "Wire up a test dataset", "evaluations.setupWizard.step1.item1": "Set up a Google Sheet with one input per row", "evaluations.setupWizard.step1.item2": "Add an evaluation trigger to your workflow and wire it up", "evaluations.setupWizard.step1.button": "Add evaluation trigger", "evaluations.setupWizard.step2.title": "Write workflow outputs back to dataset", "evaluations.setupWizard.step2.item1": "Add a 'set outputs' operation to log each output back to Google Sheets", "evaluations.setupWizard.step2.button": "Add 'set outputs' node", "evaluations.setupWizard.step3.title": "Set up a quality score", "evaluations.setupWizard.step3.item1": "Calculate a score, e.g. by comparing expected and actual outputs", "evaluations.setupWizard.step3.item2": "Add a 'set metrics' operation to log the score", "evaluations.setupWizard.step3.button": "Add 'Set metrics' node", "evaluations.setupWizard.step3.skip": "<PERSON><PERSON>", "evaluations.setupWizard.step3.notice": "Your plan supports custom metrics for one workflow only. {link}", "evaluations.setupWizard.step3.notice.link": "See plans", "evaluations.setupWizard.step4.title": "Run evaluation", "evaluations.setupWizard.step4.button": "Run evaluation", "evaluations.setupWizard.step4.altButton": "Run in editor", "evaluations.setupWizard.limitReached": "Limit reached. Your plan includes custom metrics for one workflow only. Upgrade for unlimited use or delete the workflow with existing evaluation runs.", "freeAi.credits.callout.claim.title": "Get {credits} free OpenAI API credits", "freeAi.credits.callout.claim.button.label": "Claim credits", "freeAi.credits.callout.success.title.part1": "Claimed {credits} free OpenAI API credits! Please note these free credits are only for the following models:", "freeAi.credits.callout.success.title.part2": "gpt-4o-mini, text-embedding-3-small, dall-e-3, tts-1, whisper-1, and text-moderation-latest", "freeAi.credits.credentials.edit": "This is a managed credential and cannot be edited.", "freeAi.credits.showError.claim.title": "Free AI credits", "freeAi.credits.showError.claim.message": "Enable to claim credits", "freeAi.credits.showWarning.workflow.activation.title": "You're using free OpenAI API credits", "freeAi.credits.showWarning.workflow.activation.description": "To make sure your workflow runs smoothly in the future, replace the free OpenAI API credits with your own API key.", "insights.heading": "Insights", "insights.lastNDays": "Last {count} days", "insights.lastNHours": "Last {count} hours", "insights.months": "{count} months", "insights.oneYear": "One year", "insights.banner.timeSaved.tooltip": "No estimate available yet. To see potential time savings, {link} to each workflow from workflow settings.", "insights.banner.timeSaved.tooltip.link.text": "add time estimates", "insights.banner.noData.tooltip": "Manual executions aren’t counted. Data may take up to 24 hours to update after upgrading. {link}", "insights.banner.noData.tooltip.link": "Learn more", "insights.banner.noData.tooltip.link.url": "https://docs.n8n.io/insights/", "insights.banner.title.total": "Prod. executions", "insights.banner.title.failed": "Failed prod. executions", "insights.banner.title.failureRate": "Failure rate", "insights.banner.title.timeSaved": "Time saved", "insights.banner.title.timeSavedDailyAverage": "Time saved daily avg.", "insights.banner.title.averageRunTime": "Run time (avg.)", "insights.dashboard.table.projectName": "Project name", "insights.dashboard.table.estimate": "Estimate", "insights.dashboard.title": "Insights", "insights.dashboard.paywall.cta": "Upgrade", "insights.dashboard.paywall.title": "Upgrade to access more detailed insights", "insights.dashboard.paywall.description": "Gain access to more granular, per-workflow insights and visual breakdown of production executions over different time periods.", "insights.banner.title.timeSaved.tooltip": "Total time saved calculated from your estimated time savings per execution across all workflows", "insights.banner.failureRate.deviation.tooltip": "Percentage point change from previous period", "insights.chart.failed": "Failed", "insights.chart.succeeded": "Successful", "insights.chart.loading": "Loading data", "communityNodesDocsLink.link.title": "Open community node docs", "communityNodesDocsLink.title": "Docs", "communityNodeItem.node.hint": "Install this node to start using it", "communityNodeItem.actions.hint": "Install this node to start using actions", "communityNodeItem.label": "Add to workflow", "communityNodeDetails.installed": "Installed", "communityNodeDetails.install": "Install node", "communityNodeInfo.approved": "This community node has been reviewed and approved by n8n", "communityNodeInfo.officialApproved": "This node has been reviewed and approved by n8n", "communityNodeInfo.approved.label": "Verified", "communityNodeInfo.unverified": "This community node was added via npm and has not been verified by n8n", "communityNodeInfo.unverified.label": "Via npm", "communityNodeInfo.downloads": "{downloads} Downloads", "communityNodeInfo.publishedBy": "Published by {publisherName}", "communityNodeInfo.contact.admin": "Please contact an administrator to install this community node:", "insights.upgradeModal.button.dismiss": "<PERSON><PERSON><PERSON>", "insights.upgradeModal.button.upgrade": "Upgrade", "insights.upgradeModal.content": "Viewing this time period requires an enterprise plan. Upgrade to Enterprise to unlock advanced features.", "insights.upgradeModal.perks.0": "View up to one year of insights history", "insights.upgradeModal.perks.1": "Zoom into last 24 hours with hourly granularity", "insights.upgradeModal.perks.2": "Gain deeper visibility into workflow trends over time", "insights.upgradeModal.title": "Upgrade to Enterprise", "whatsNew.versionsBehind": "{count} version behind | {count} versions behind", "whatsNew.update": "Update", "whatsNew.updateAvailable": "You're currently on version {currentVersion}. Update to {latestVersion} to get {count} versions worth of new features, improvements, and fixes. See what changed", "whatsNew.updateAvailable.changelogLink": "in the full changelog"}