{"type": "module", "name": "@n8n/design-system", "version": "1.90.0", "main": "src/index.ts", "import": "src/index.ts", "scripts": {"dev": "pnpm run storybook", "clean": "rimraf dist .turbo", "build": "vite build", "typecheck": "vue-tsc --noEmit", "typecheck:watch": "vue-tsc --watch --noEmit", "test": "vitest run", "test:dev": "vitest", "build:storybook": "storybook build", "storybook": "storybook dev -p 6006 --no-open", "chromatic": "chromatic", "format": "biome format --write .  && prettier --write . --ignore-path ../../../../.prettierignore", "format:check": "biome ci .  && prettier --check . --ignore-path ../../../../.prettierignore", "lint": "eslint src --quiet", "lintfix": "eslint src --fix"}, "devDependencies": {"@n8n/eslint-config": "workspace:*", "@n8n/storybook": "workspace:*", "@n8n/typescript-config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@testing-library/jest-dom": "catalog:frontend", "@testing-library/user-event": "catalog:frontend", "@testing-library/vue": "catalog:frontend", "@types/lodash": "catalog:", "@types/markdown-it": "^13.0.9", "@types/markdown-it-emoji": "^2.0.2", "@types/markdown-it-link-attributes": "^3.0.5", "@types/sanitize-html": "^2.11.0", "@vitejs/plugin-vue": "catalog:frontend", "@vitest/coverage-v8": "catalog:", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "sass": "^1.64.1", "tailwindcss": "^3.4.3", "unplugin-icons": "catalog:frontend", "unplugin-vue-components": "catalog:frontend", "vite": "catalog:", "vitest": "catalog:", "vitest-mock-extended": "catalog:", "vue-tsc": "catalog:frontend"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/vue-fontawesome": "^3.0.3", "@n8n/composables": "workspace:*", "@n8n/utils": "workspace:*", "@tanstack/vue-table": "^8.21.2", "element-plus": "catalog:frontend", "is-emoji-supported": "^0.0.5", "lodash": "catalog:", "markdown-it": "^13.0.2", "markdown-it-emoji": "^2.0.2", "markdown-it-link-attributes": "^4.0.1", "markdown-it-task-lists": "^2.1.1", "parse-diff": "^0.11.1", "reka-ui": "^2.2.1", "sanitize-html": "2.12.1", "vue": "catalog:frontend", "vue-boring-avatars": "^1.3.0", "vue-router": "catalog:frontend", "xss": "catalog:"}, "peerDependencies": {"@vueuse/core": "*"}}