import Binary from './custom/binary.svg';
import BoltFilled from './custom/bolt-filled.svg';
import GripLinesVertical from './custom/grip-lines-vertical.svg';
import Json from './custom/json.svg';
import PopOut from './custom/pop-out.svg';
import Schema from './custom/schema.svg';
import Spinner from './custom/spinner.svg';
import StatusCanceled from './custom/status-canceled.svg';
import StatusCompleted from './custom/status-completed.svg';
import StatusError from './custom/status-error.svg';
import StatusNew from './custom/status-new.svg';
import StatusUnknown from './custom/status-unknown.svg';
import StatusWaiting from './custom/status-waiting.svg';
import StatusWarning from './custom/status-warning.svg';
import Text from './custom/text.svg';
import Toolbox from './custom/toolbox.svg';
import Triangle from './custom/triangle.svg';
import VectorSquare from './custom/vector-square.svg';

import IconLucideAlignRight from '~icons/lucide/align-right';
import IconLucideArchive from '~icons/lucide/archive';
import IconLucideArrowDown from '~icons/lucide/arrow-down';
import IconLucideArrowLeft from '~icons/lucide/arrow-left';
import IconLucideArrowLeftRight from '~icons/lucide/arrow-left-right';
import IconLucideArrowRight from '~icons/lucide/arrow-right';
import IconLucideArrowRightFromLine from '~icons/lucide/arrow-right-from-line';
import IconLucideArrowRightToLine from '~icons/lucide/arrow-right-to-line';
import IconLucideArrowUp from '~icons/lucide/arrow-up';
import IconLucideAtSign from '~icons/lucide/at-sign';
import IconLucideBan from '~icons/lucide/ban';
import IconLucideBell from '~icons/lucide/bell';
import IconLucideBook from '~icons/lucide/book';
import IconLucideBot from '~icons/lucide/bot';
import IconLucideBox from '~icons/lucide/box';
import IconLucideBrain from '~icons/lucide/brain';
import IconLucideBug from '~icons/lucide/bug';
import IconLucideCalculator from '~icons/lucide/calculator';
import IconLucideCalendar from '~icons/lucide/calendar';
import IconLucideCaseUpper from '~icons/lucide/case-upper';
import IconLucideChartColumnDecreasing from '~icons/lucide/chart-column-decreasing';
import IconLucideCheck from '~icons/lucide/check';
import IconLucideCheckCheck from '~icons/lucide/check-check';
import IconLucideChevronDown from '~icons/lucide/chevron-down';
import IconLucideChevronLeft from '~icons/lucide/chevron-left';
import IconLucideChevronRight from '~icons/lucide/chevron-right';
import IconLucideChevronUp from '~icons/lucide/chevron-up';
import IconLucideChevronsDownUp from '~icons/lucide/chevrons-down-up';
import IconLucideChevronsLeft from '~icons/lucide/chevrons-left';
import IconLucideChevronsUpDown from '~icons/lucide/chevrons-up-down';
import IconLucideCircle from '~icons/lucide/circle';
import IconLucideCircleAlert from '~icons/lucide/circle-alert';
import IconLucideCircleCheck from '~icons/lucide/circle-check';
import IconLucideCircleDot from '~icons/lucide/circle-dot';
import IconLucideCircleHelp from '~icons/lucide/circle-help';
import IconLucideCircleMinus from '~icons/lucide/circle-minus';
import IconLucideCirclePause from '~icons/lucide/circle-pause';
import IconLucideCirclePlay from '~icons/lucide/circle-play';
import IconLucideCirclePlus from '~icons/lucide/circle-plus';
import IconLucideCircleUserRound from '~icons/lucide/circle-user-round';
import IconLucideCircleX from '~icons/lucide/circle-x';
import IconLucideClipboardList from '~icons/lucide/clipboard-list';
import IconLucideClock from '~icons/lucide/clock';
import IconLucideCloud from '~icons/lucide/cloud';
import IconLucideCloudDownload from '~icons/lucide/cloud-download';
import IconLucideCode from '~icons/lucide/code';
import IconLucideCog from '~icons/lucide/cog';
import IconLucideContrast from '~icons/lucide/contrast';
import IconLucideCopy from '~icons/lucide/copy';
import IconLucideCrosshair from '~icons/lucide/crosshair';
import IconLucideDatabase from '~icons/lucide/database';
import IconLucideEarth from '~icons/lucide/earth';
import IconLucideEllipsis from '~icons/lucide/ellipsis';
import IconLucideEllipsisVertical from '~icons/lucide/ellipsis-vertical';
import IconLucideEqual from '~icons/lucide/equal';
import IconLucideExternalLink from '~icons/lucide/external-link';
import IconLucideEye from '~icons/lucide/eye';
import IconLucideEyeOff from '~icons/lucide/eye-off';
import IconLucideFile from '~icons/lucide/file';
import IconLucideFileArchive from '~icons/lucide/file-archive';
import IconLucideFileCode from '~icons/lucide/file-code';
import IconLucideFileDown from '~icons/lucide/file-down';
import IconLucideFileInput from '~icons/lucide/file-input';
import IconLucideFileOutput from '~icons/lucide/file-output';
import IconLucideFileText from '~icons/lucide/file-text';
import IconLucideFiles from '~icons/lucide/files';
import IconLucideFingerprint from '~icons/lucide/fingerprint';
import IconLucideFlaskConical from '~icons/lucide/flask-conical';
import IconLucideFolder from '~icons/lucide/folder';
import IconLucideFolderOpen from '~icons/lucide/folder-open';
import IconLucideFolderPlus from '~icons/lucide/folder-plus';
import IconLucideFunnel from '~icons/lucide/funnel';
import IconLucideGem from '~icons/lucide/gem';
import IconLucideGift from '~icons/lucide/gift';
import IconLucideGitBranch from '~icons/lucide/git-branch';
import IconLucideGlobe from '~icons/lucide/globe';
import IconLucideGraduationCap from '~icons/lucide/graduation-cap';
import IconLucideGrid2x2 from '~icons/lucide/grid-2x2';
import IconLucideGripVertical from '~icons/lucide/grip-vertical';
import IconLucideHandCoins from '~icons/lucide/hand-coins';
import IconLucideHandshake from '~icons/lucide/handshake';
import IconLucideHardDrive from '~icons/lucide/hard-drive';
import IconLucideHardDriveDownload from '~icons/lucide/hard-drive-download';
import IconLucideHash from '~icons/lucide/hash';
import IconLucideHistory from '~icons/lucide/history';
import IconLucideHourglass from '~icons/lucide/hourglass';
import IconLucideHouse from '~icons/lucide/house';
import IconLucideImage from '~icons/lucide/image';
import IconLucideInbox from '~icons/lucide/inbox';
import IconLucideInfo from '~icons/lucide/info';
import IconLucideKeyRound from '~icons/lucide/key-round';
import IconLucideLanguages from '~icons/lucide/languages';
import IconLucideLayers from '~icons/lucide/layers';
import IconLucideLightbulb from '~icons/lucide/lightbulb';
import IconLucideLink from '~icons/lucide/link';
import IconLucideList from '~icons/lucide/list';
import IconLucideListChecks from '~icons/lucide/list-checks';
import IconLucideLock from '~icons/lucide/lock';
import IconLucideLogIn from '~icons/lucide/log-in';
import IconLucideLogOut from '~icons/lucide/log-out';
import IconLucideMail from '~icons/lucide/mail';
import IconLucideMaximize from '~icons/lucide/maximize';
import IconLucideMaximize2 from '~icons/lucide/maximize-2';
import IconLucideMenu from '~icons/lucide/menu';
import IconLucideMessageCircle from '~icons/lucide/message-circle';
import IconLucideMessagesSquare from '~icons/lucide/messages-square';
import IconLucideMilestone from '~icons/lucide/milestone';
import IconLucideMinimize2 from '~icons/lucide/minimize-2';
import IconLucideMousePointer from '~icons/lucide/mouse-pointer';
import IconLucideNetwork from '~icons/lucide/network';
import IconLucidePackageOpen from '~icons/lucide/package-open';
import IconLucidePalette from '~icons/lucide/palette';
import IconLucidePause from '~icons/lucide/pause';
import IconLucidePen from '~icons/lucide/pen';
import IconLucidePencil from '~icons/lucide/pencil';
import IconLucidePin from '~icons/lucide/pin';
import IconLucidePlay from '~icons/lucide/play';
import IconLucidePlug from '~icons/lucide/plug';
import IconLucidePlus from '~icons/lucide/plus';
import IconLucidePocketKnife from '~icons/lucide/pocket-knife';
import IconLucidePower from '~icons/lucide/power';
import IconLucideRedo2 from '~icons/lucide/redo-2';
import IconLucideRefreshCw from '~icons/lucide/refresh-cw';
import IconLucideRemoveFormatting from '~icons/lucide/remove-formatting';
import IconLucideRss from '~icons/lucide/rss';
import IconLucideSatelliteDish from '~icons/lucide/satellite-dish';
import IconLucideSave from '~icons/lucide/save';
import IconLucideScale from '~icons/lucide/scale';
import IconLucideScissors from '~icons/lucide/scissors';
import IconLucideSearch from '~icons/lucide/search';
import IconLucideSend from '~icons/lucide/send';
import IconLucideServer from '~icons/lucide/server';
import IconLucideShare from '~icons/lucide/share';
import IconLucideSlidersHorizontal from '~icons/lucide/sliders-horizontal';
import IconLucideSmile from '~icons/lucide/smile';
import IconLucideSquare from '~icons/lucide/square';
import IconLucideSquareCheck from '~icons/lucide/square-check';
import IconLucideSquarePen from '~icons/lucide/square-pen';
import IconLucideSquarePlus from '~icons/lucide/square-plus';
import IconLucideStickyNote from '~icons/lucide/sticky-note';
import IconLucideSun from '~icons/lucide/sun';
import IconLucideTable from '~icons/lucide/table';
import IconLucideTags from '~icons/lucide/tags';
import IconLucideTerminal from '~icons/lucide/terminal';
import IconLucideThumbsDown from '~icons/lucide/thumbs-down';
import IconLucideThumbsUp from '~icons/lucide/thumbs-up';
import IconLucideTrash2 from '~icons/lucide/trash-2';
import IconLucideTreePine from '~icons/lucide/tree-pine';
import IconLucideTriangleAlert from '~icons/lucide/triangle-alert';
import IconLucideUndo2 from '~icons/lucide/undo-2';
import IconLucideUnlink from '~icons/lucide/unlink';
import IconLucideUser from '~icons/lucide/user';
import IconLucideUserCheck from '~icons/lucide/user-check';
import IconLucideUserLock from '~icons/lucide/user-lock';
import IconLucideUserRound from '~icons/lucide/user-round';
import IconLucideUsers from '~icons/lucide/users';
import IconLucideVariable from '~icons/lucide/variable';
import IconLucideVault from '~icons/lucide/vault';
import IconLucideVideo from '~icons/lucide/video';
import IconLucideWaypoints from '~icons/lucide/waypoints';
import IconLucideWrench from '~icons/lucide/wrench';
import IconLucideX from '~icons/lucide/x';
import IconLucideZap from '~icons/lucide/zap';
import IconLucideZoomIn from '~icons/lucide/zoom-in';
import IconLucideZoomOut from '~icons/lucide/zoom-out';

/**
 * Need to keep old icon names
 * To support old project icons
 * Which used to include all icons in instance
 */
export const deprecatedIconSet = {
	// customIcons
	variable: IconLucideVariable,
	'pop-out': PopOut,
	triangle: Triangle,
	'status-completed': StatusCompleted,
	'status-waiting': StatusWaiting,
	'status-error': StatusError,
	'status-canceled': StatusCanceled,
	'status-new': StatusNew,
	'status-unknown': StatusUnknown,
	'status-warning': StatusWarning,
	'vector-square': VectorSquare,
	schema: Schema,
	json: Json,
	binary: Binary,
	text: Text,
	toolbox: Toolbox,
	spinner: Spinner,
	xmark: IconLucideX,

	// fontAwesomeIcons
	'caret-up': IconLucideChevronUp,
	'caret-down': IconLucideChevronDown,
	'caret-right': IconLucideChevronRight,
	'caret-left': IconLucideChevronLeft,
	'folder-plus': IconLucideFolderPlus,
	share: IconLucideShare,
	'user-check': IconLucideUserCheck,
	'check-double': IconLucideCheckCheck,
	'exclamation-circle': IconLucideCircleAlert,
	circle: IconLucideCircle,
	'eye-slash': IconLucideEyeOff,
	folder: IconLucideFolder,
	'minus-circle': IconLucideCircleMinus,
	adjust: IconLucideContrast,
	refresh: IconLucideRefreshCw,
	vault: IconLucideVault,
	'angle-double-left': IconLucideChevronsLeft,
	'angle-down': IconLucideChevronDown,
	'angle-left': IconLucideChevronLeft,
	'angle-right': IconLucideChevronRight,
	'angle-up': IconLucideChevronUp,
	archive: IconLucideArchive,
	'arrow-left': IconLucideArrowLeft,
	'arrow-right': IconLucideArrowRight,
	'arrow-up': IconLucideArrowUp,
	'arrow-down': IconLucideArrowDown,
	at: IconLucideAtSign,
	ban: IconLucideBan,
	'balance-scale-left': IconLucideScale,
	bars: IconLucideMenu,
	bolt: IconLucideZap,
	book: IconLucideBook,
	'box-open': IconLucidePackageOpen,
	bug: IconLucideBug,
	brain: IconLucideBrain,
	calculator: IconLucideCalculator,
	calendar: IconLucideCalendar,
	'chart-bar': IconLucideChartColumnDecreasing,
	check: IconLucideCheck,
	'check-circle': IconLucideCircleCheck,
	'check-square': IconLucideSquareCheck,
	'chevron-left': IconLucideChevronLeft,
	'chevron-right': IconLucideChevronRight,
	'chevron-down': IconLucideChevronDown,
	'chevron-up': IconLucideChevronUp,
	code: IconLucideCode,
	'code-branch': IconLucideGitBranch,
	cog: IconLucideCog,
	cogs: IconLucideCog,
	comment: IconLucideMessageCircle,
	comments: IconLucideMessagesSquare,
	'clipboard-list': IconLucideClipboardList,
	clock: IconLucideClock,
	clone: IconLucideCopy,
	cloud: IconLucideCloud,
	'cloud-download-alt': IconLucideCloudDownload,
	compress: IconLucideChevronsUpDown,
	copy: IconLucideFiles,
	cube: IconLucideBox,
	cut: IconLucideScissors,
	database: IconLucideDatabase,
	'dot-circle': IconLucideCircleDot,
	'grip-lines-vertical': GripLinesVertical,
	'grip-vertical': IconLucideGripVertical,
	edit: IconLucideSquarePen,
	'ellipsis-h': IconLucideEllipsis,
	'ellipsis-v': IconLucideEllipsisVertical,
	envelope: IconLucideMail,
	equals: IconLucideEqual,
	eye: IconLucideEye,
	'exclamation-triangle': IconLucideTriangleAlert,
	expand: IconLucideMaximize,
	'expand-alt': IconLucideMaximize2,
	'external-link-alt': IconLucideExternalLink,
	'exchange-alt': IconLucideArrowLeftRight,
	file: IconLucideFile,
	'file-alt': IconLucideFileText,
	'file-archive': IconLucideFileArchive,
	'file-code': IconLucideFileCode,
	'file-download': IconLucideFileDown,
	'file-export': IconLucideFileOutput,
	'file-import': IconLucideFileInput,
	'file-pdf': IconLucideFileText,
	filter: IconLucideFunnel,
	fingerprint: IconLucideFingerprint,
	flask: IconLucideFlaskConical,
	'folder-open': IconLucideFolderOpen,
	font: IconLucideCaseUpper,
	gift: IconLucideGift,
	globe: IconLucideGlobe,
	'globe-americas': IconLucideEarth,
	'graduation-cap': IconLucideGraduationCap,
	'hand-holding-usd': IconLucideHandCoins,
	'hand-scissors': IconLucideScissors,
	handshake: IconLucideHandshake,
	'hand-point-left': IconLucideArrowLeft,
	hashtag: IconLucideHash,
	hdd: IconLucideHardDrive,
	history: IconLucideHistory,
	home: IconLucideHouse,
	hourglass: IconLucideHourglass,
	image: IconLucideImage,
	inbox: IconLucideInbox,
	info: IconLucideInfo,
	'info-circle': IconLucideInfo,
	key: IconLucideKeyRound,
	language: IconLucideLanguages,
	'layer-group': IconLucideLayers,
	link: IconLucideLink,
	list: IconLucideList,
	lightbulb: IconLucideLightbulb,
	lock: IconLucideLock,
	'map-signs': IconLucideMilestone,
	'mouse-pointer': IconLucideMousePointer,
	'network-wired': IconLucideNetwork,
	palette: IconLucidePalette,
	pause: IconLucidePause,
	'pause-circle': IconLucideCirclePause,
	pen: IconLucidePen,
	'pencil-alt': IconLucidePencil,
	play: IconLucidePlay,
	'play-circle': IconLucideCirclePlay,
	plug: IconLucidePlug,
	plus: IconLucidePlus,
	'plus-circle': IconLucideCirclePlus,
	'plus-square': IconLucideSquarePlus,
	'project-diagram': IconLucideWaypoints,
	question: IconLucideCircleHelp,
	'question-circle': IconLucideCircleHelp,
	redo: IconLucideRedo2,
	'remove-format': IconLucideRemoveFormatting,
	robot: IconLucideBot,
	rss: IconLucideRss,
	save: IconLucideSave,
	'satellite-dish': IconLucideSatelliteDish,
	search: IconLucideSearch,
	'search-minus': IconLucideZoomOut,
	'search-plus': IconLucideZoomIn,
	server: IconLucideServer,
	screwdriver: IconLucidePocketKnife,
	smile: IconLucideSmile,
	'sign-in-alt': IconLucideLogIn,
	'sign-out-alt': IconLucideLogOut,
	'sliders-h': IconLucideSlidersHorizontal,
	'sticky-note': IconLucideStickyNote,
	stop: IconLucideSquare,
	stream: IconLucideAlignRight,
	sun: IconLucideSun,
	sync: IconLucideRefreshCw,
	'sync-alt': IconLucideRefreshCw,
	table: IconLucideTable,
	tags: IconLucideTags,
	tasks: IconLucideListChecks,
	terminal: IconLucideTerminal,
	'th-large': IconLucideGrid2x2,
	thumbtack: IconLucidePin,
	'thumbs-down': IconLucideThumbsDown,
	'thumbs-up': IconLucideThumbsUp,
	times: IconLucideX,
	'times-circle': IconLucideCircleX,
	tools: IconLucideWrench,
	trash: IconLucideTrash2,
	undo: IconLucideUndo2,
	unlink: IconLucideUnlink,
	user: IconLucideUser,
	'user-circle': IconLucideCircleUserRound,
	'user-friends': IconLucideUserRound,
	users: IconLucideUsers,
	video: IconLucideVideo,
	tree: IconLucideTreePine,
	'user-lock': IconLucideUserLock,
	gem: IconLucideGem,
	download: IconLucideHardDriveDownload,
	'power-off': IconLucidePower,
	'paper-plane': IconLucideSend,
	bell: IconLucideBell,
} as const;

export const updatedIconSet = {
	// custom icons
	// NOTE: ensure to replace any colors with "currentColor" in SVG
	'bolt-filled': BoltFilled,
	'grip-lines-vertical': GripLinesVertical,
	variable: IconLucideVariable,
	'pop-out': PopOut,
	triangle: Triangle,
	'status-completed': StatusCompleted,
	'status-waiting': StatusWaiting,
	'status-error': StatusError,
	'status-canceled': StatusCanceled,
	'status-new': StatusNew,
	'status-unknown': StatusUnknown,
	'status-warning': StatusWarning,
	'vector-square': VectorSquare,
	schema: Schema,
	json: Json,
	binary: Binary,
	text: Text,
	toolbox: Toolbox,
	spinner: Spinner,

	// lucide
	'align-right': IconLucideAlignRight,
	archive: IconLucideArchive,
	'arrow-down': IconLucideArrowDown,
	'arrow-left': IconLucideArrowLeft,
	'arrow-left-right': IconLucideArrowLeftRight,
	'arrow-right': IconLucideArrowRight,
	'arrow-right-from-line': IconLucideArrowRightFromLine,
	'arrow-right-to-line': IconLucideArrowRightToLine,
	'arrow-up': IconLucideArrowUp,
	'at-sign': IconLucideAtSign,
	ban: IconLucideBan,
	bell: IconLucideBell,
	book: IconLucideBook,
	bot: IconLucideBot,
	box: IconLucideBox,
	brain: IconLucideBrain,
	bug: IconLucideBug,
	calculator: IconLucideCalculator,
	calendar: IconLucideCalendar,
	'case-upper': IconLucideCaseUpper,
	'chart-column-decreasing': IconLucideChartColumnDecreasing,
	check: IconLucideCheck,
	'check-check': IconLucideCheckCheck,
	'chevron-down': IconLucideChevronDown,
	'chevron-left': IconLucideChevronLeft,
	'chevron-right': IconLucideChevronRight,
	'chevron-up': IconLucideChevronUp,
	'chevrons-left': IconLucideChevronsLeft,
	'chevrons-down-up': IconLucideChevronsDownUp,
	'chevrons-up-down': IconLucideChevronsUpDown,
	circle: IconLucideCircle,
	'circle-alert': IconLucideCircleAlert,
	'circle-check': IconLucideCircleCheck,
	'circle-dot': IconLucideCircleDot,
	'circle-help': IconLucideCircleHelp,
	'circle-minus': IconLucideCircleMinus,
	'circle-pause': IconLucideCirclePause,
	'circle-play': IconLucideCirclePlay,
	'circle-plus': IconLucideCirclePlus,
	'circle-user-round': IconLucideCircleUserRound,
	'circle-x': IconLucideCircleX,
	'clipboard-list': IconLucideClipboardList,
	clock: IconLucideClock,
	cloud: IconLucideCloud,
	'cloud-download': IconLucideCloudDownload,
	code: IconLucideCode,
	cog: IconLucideCog,
	contrast: IconLucideContrast,
	copy: IconLucideCopy,
	crosshair: IconLucideCrosshair,
	database: IconLucideDatabase,
	earth: IconLucideEarth,
	ellipsis: IconLucideEllipsis,
	'ellipsis-vertical': IconLucideEllipsisVertical,
	equal: IconLucideEqual,
	'external-link': IconLucideExternalLink,
	eye: IconLucideEye,
	'eye-off': IconLucideEyeOff,
	file: IconLucideFile,
	'file-archive': IconLucideFileArchive,
	'file-code': IconLucideFileCode,
	'file-down': IconLucideFileDown,
	'file-input': IconLucideFileInput,
	'file-output': IconLucideFileOutput,
	'file-text': IconLucideFileText,
	files: IconLucideFiles,
	fingerprint: IconLucideFingerprint,
	'flask-conical': IconLucideFlaskConical,
	folder: IconLucideFolder,
	'folder-open': IconLucideFolderOpen,
	'folder-plus': IconLucideFolderPlus,
	funnel: IconLucideFunnel,
	gem: IconLucideGem,
	gift: IconLucideGift,
	'git-branch': IconLucideGitBranch,
	globe: IconLucideGlobe,
	'graduation-cap': IconLucideGraduationCap,
	'grid-2x2': IconLucideGrid2x2,
	'grip-vertical': IconLucideGripVertical,
	'hand-coins': IconLucideHandCoins,
	handshake: IconLucideHandshake,
	'hard-drive': IconLucideHardDrive,
	'hard-drive-download': IconLucideHardDriveDownload,
	hash: IconLucideHash,
	history: IconLucideHistory,
	hourglass: IconLucideHourglass,
	house: IconLucideHouse,
	image: IconLucideImage,
	inbox: IconLucideInbox,
	info: IconLucideInfo,
	'key-round': IconLucideKeyRound,
	languages: IconLucideLanguages,
	layers: IconLucideLayers,
	lightbulb: IconLucideLightbulb,
	link: IconLucideLink,
	list: IconLucideList,
	'list-checks': IconLucideListChecks,
	lock: IconLucideLock,
	'log-in': IconLucideLogIn,
	'log-out': IconLucideLogOut,
	mail: IconLucideMail,
	'minimize-2': IconLucideMinimize2,
	maximize: IconLucideMaximize,
	'maximize-2': IconLucideMaximize2,
	menu: IconLucideMenu,
	'message-circle': IconLucideMessageCircle,
	'messages-square': IconLucideMessagesSquare,
	milestone: IconLucideMilestone,
	'mouse-pointer': IconLucideMousePointer,
	network: IconLucideNetwork,
	'package-open': IconLucidePackageOpen,
	palette: IconLucidePalette,
	pause: IconLucidePause,
	pen: IconLucidePen,
	pencil: IconLucidePencil,
	pin: IconLucidePin,
	play: IconLucidePlay,
	plug: IconLucidePlug,
	plus: IconLucidePlus,
	'pocket-knife': IconLucidePocketKnife,
	power: IconLucidePower,
	'redo-2': IconLucideRedo2,
	'refresh-cw': IconLucideRefreshCw,
	'remove-formatting': IconLucideRemoveFormatting,
	rss: IconLucideRss,
	'satellite-dish': IconLucideSatelliteDish,
	save: IconLucideSave,
	scale: IconLucideScale,
	scissors: IconLucideScissors,
	search: IconLucideSearch,
	send: IconLucideSend,
	server: IconLucideServer,
	share: IconLucideShare,
	'sliders-horizontal': IconLucideSlidersHorizontal,
	smile: IconLucideSmile,
	square: IconLucideSquare,
	'square-check': IconLucideSquareCheck,
	'square-pen': IconLucideSquarePen,
	'square-plus': IconLucideSquarePlus,
	'sticky-note': IconLucideStickyNote,
	sun: IconLucideSun,
	table: IconLucideTable,
	tags: IconLucideTags,
	terminal: IconLucideTerminal,
	'thumbs-down': IconLucideThumbsDown,
	'thumbs-up': IconLucideThumbsUp,
	'trash-2': IconLucideTrash2,
	'tree-pine': IconLucideTreePine,
	'triangle-alert': IconLucideTriangleAlert,
	'undo-2': IconLucideUndo2,
	unlink: IconLucideUnlink,
	user: IconLucideUser,
	'user-check': IconLucideUserCheck,
	'user-lock': IconLucideUserLock,
	'user-round': IconLucideUserRound,
	users: IconLucideUsers,
	vault: IconLucideVault,
	video: IconLucideVideo,
	waypoints: IconLucideWaypoints,
	wrench: IconLucideWrench,
	x: IconLucideX,
	zap: IconLucideZap,
	'zoom-in': IconLucideZoomIn,
	'zoom-out': IconLucideZoomOut,
} as const;

export type IconName = keyof typeof updatedIconSet; // only new icon names should be used moving forward

export function isSupportedIconName(iconName?: string): iconName is IconName {
	// support both deprecated and updated icon names
	return (
		typeof iconName === 'string' && (iconName in updatedIconSet || iconName in deprecatedIconSet)
	);
}
