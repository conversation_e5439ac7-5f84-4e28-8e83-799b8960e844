@use 'sass:math';

@mixin theme {
	// Primary tokens
	--color-danger: var(--prim-color-alt-k);

	// Text
	--color-text-dark: var(--prim-gray-40);
	--color-text-base: var(--prim-gray-200);
	--color-text-light: var(--prim-gray-320);
	--color-text-lighter: var(--prim-gray-740);
	--color-text-xlight: var(--prim-gray-820);
	--color-text-danger: var(--prim-color-alt-c-tint-150);

	// Foreground
	--color-foreground-xdark: var(--prim-gray-200);
	--color-foreground-dark: var(--prim-gray-420);
	--color-foreground-base: var(--prim-gray-670);
	--color-foreground-light: var(--prim-gray-740);
	--color-foreground-xlight: var(--prim-gray-820);

	// Background
	--color-background-dark: var(--prim-gray-70);
	--color-background-medium: var(--prim-gray-540);
	--color-background-base: var(--prim-gray-670);
	--color-background-light-base: var(--prim-gray-780);
	--color-background-light: var(--prim-gray-820);
	--color-background-xlight: var(--prim-gray-740);

	--box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 6px rgba(0, 0, 0, 0.1);
	--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 6px rgba(0, 0, 0, 0.2);
	--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

	// Secondary tokens

	// AI Assistant
	--color-assistant-highlight-1: #8c90f2;
	--color-assistant-highlight-2: #a977f0;
	--color-assistant-highlight-3: #f0778b;
	--color-askAssistant-button-background: #2e2e2e;
	--color-askAssistant-button-background-hover: #383839;
	--color-askAssistant-button-background-active: #414244;
	--color-assistant-inner-highlight-hover: var(--color-askAssistant-button-background-hover);
	--color-assistant-inner-highlight-active: var(--color-askAssistant-button-background-active);

	--color-assistant-highlight-gradient: linear-gradient(
		105deg,
		var(--color-assistant-highlight-1) 0%,
		var(--color-assistant-highlight-2) 50%,
		var(--color-assistant-highlight-3) 100%
	);
	--color-assistant-highlight-reverse: linear-gradient(
		105deg,
		var(--color-assistant-highlight-3) 0%,
		var(--color-assistant-highlight-2) 50%,
		var(--color-assistant-highlight-1) 100%
	);

	// LangChain
	--color-lm-chat-messages-background: var(--prim-gray-820);
	--color-lm-chat-bot-background: var(--prim-gray-740);
	--color-lm-chat-bot-border: var(--prim-gray-490);
	--color-lm-chat-user-background: var(--prim-color-alt-a-shade-100);
	--color-lm-chat-user-border: var(--prim-color-alt-a);

	// Canvas
	--color-canvas-background: var(--prim-gray-820);
	--color-canvas-background-h: var(--prim-gray-h); // Used for connectors labels background
	--color-canvas-background-s: 1%;
	--color-canvas-background-l: 18%;
	--color-canvas-dot: var(--prim-gray-670);
	--color-canvas-read-only-line: var(--prim-gray-800);
	--color-canvas-selected: var(--prim-gray-0-alpha-025);
	--color-canvas-selected-transparent: var(--color-canvas-selected);
	--color-canvas-label-background: hsla(
		var(--color-canvas-background-h),
		var(--color-canvas-background-s),
		var(--color-canvas-background-l),
		0.85
	);

	// Nodes
	--color-node-background: var(--prim-gray-740);
	--color-node-executing-background: var(--prim-gray-670);
	--color-node-executing-other-background: var(--prim-gray-670);
	--color-node-pinned-border: var(--prim-color-secondary-tint-100);
	--node-type-main-color: var(--prim-gray-420);

	// Sticky
	--color-sticky-background: var(--prim-color-alt-d-shade-700);
	--color-sticky-border: var(--prim-color-alt-d-shade-600);
	--color-sticky-font: var(--prim-gray-40);
	--color-sticky-code-font: var(--prim-color-secondary-tint-300);
	--color-sticky-code-background: var(--prim-gray-70-alpha-01);

	--color-sticky-background-1: var(--prim-color-alt-d-shade-700);
	--color-sticky-border-1: var(--prim-color-alt-d-shade-600);
	--color-sticky-background-2: var(--prim-color-alt-b-shade-350);
	--color-sticky-border-2: var(--prim-color-alt-b-shade-250);
	--color-sticky-background-3: var(--prim-color-alt-c-shade-250);
	--color-sticky-border-3: var(--prim-color-alt-c-shade-150);
	--color-sticky-background-4: var(--prim-color-alt-a-shade-200);
	--color-sticky-border-4: var(--prim-color-alt-a-shade-100);
	--color-sticky-background-5: var(--prim-color-alt-e-shade-350);
	--color-sticky-border-5: var(--prim-color-alt-e-shade-250);
	--color-sticky-background-6: var(--prim-color-secondary-shade-250);
	--color-sticky-border-6: var(--prim-color-secondary-shade-100);
	--color-sticky-background-7: var(--prim-gray-740);
	--color-sticky-border-7: var(--prim-gray-670);

	// NodeIcon
	--color-node-icon-gray: var(--prim-gray-120);
	--color-node-icon-black: var(--prim-gray-10);
	--color-node-icon-blue: #898fff;
	--color-node-icon-light-blue: #58abff;
	--color-node-icon-dark-blue: #7ba7ff;
	--color-node-icon-orange-red: var(--prim-color-primary);
	--color-node-icon-pink-red: #f85d82;
	--color-node-icon-red: var(--prim-color-alt-k);
	--color-node-icon-light-green: #20b69e;
	--color-node-icon-green: #38cb7a;
	--color-node-icon-dark-green: #86decc;
	--color-node-icon-purple: #9b6dd5;
	--color-node-icon-crimson: #f188a2;

	// Expressions, autocomplete, infobox
	--color-valid-resolvable-foreground: var(--prim-color-alt-a-tint-300);
	--color-valid-resolvable-background: var(--prim-color-alt-a-alpha-025);
	--color-invalid-resolvable-foreground: var(--prim-color-alt-c-tint-250);
	--color-invalid-resolvable-background: var(--prim-color-alt-c-alpha-02);
	--color-pending-resolvable-foreground: var(--color-text-base);
	--color-pending-resolvable-background: var(--prim-gray-70-alpha-01);
	--color-expression-editor-background: var(--prim-gray-800);
	--color-expression-editor-modal-background: var(--prim-gray-800);
	--color-expression-syntax-example: var(--prim-gray-670);
	--color-autocomplete-item-selected: var(--prim-color-secondary-tint-200);
	--color-autocomplete-section-header-border: var(--prim-gray-540);
	--color-infobox-background: var(--prim-gray-780);
	--color-infobox-examples-border-color: var(--prim-gray-670);

	// Code
	--color-code-tags-string: #9ecbff;
	--color-code-tags-regex: #9ecbff;
	--color-code-tags-primitive: #79b8ff;
	--color-code-tags-keyword: #f97583;
	--color-code-tags-variable: #79b8ff;
	--color-code-tags-parameter: #e1e4e8;
	--color-code-tags-function: #b392f0;
	--color-code-tags-constant: #79b8ff;
	--color-code-tags-property: #79b8ff;
	--color-code-tags-type: #b392f0;
	--color-code-tags-class: #b392f0;
	--color-code-tags-heading: #79b8ff;
	--color-code-tags-invalid: #f97583;
	--color-code-tags-comment: #6a737d;
	--color-json-default: var(--prim-color-secondary-tint-200);
	--color-json-null: var(--color-danger);
	--color-json-boolean: var(--prim-color-alt-a);
	--color-json-number: var(--prim-color-alt-a);
	--color-json-string: var(--prim-color-secondary-tint-200);
	--color-json-key: var(--color-text-dark);
	--color-json-brackets: var(--prim-gray-670);
	--color-json-brackets-hover: var(--prim-color-alt-e);
	--color-json-line: var(--prim-gray-200);
	--color-json-highlight: var(--color-background-base);
	--color-code-background: var(--prim-gray-820);
	--color-code-background-readonly: var(--prim-gray-740);
	--color-code-lineHighlight: var(--prim-gray-320-alpha-010);
	--color-code-foreground: var(--prim-gray-70);
	--color-code-caret: var(--prim-gray-10);
	--color-code-selection: #3392ff44;
	--color-code-selection-highlight: #17e5e633;
	--color-code-gutter-background: var(--prim-gray-820);
	--color-code-gutter-foreground: var(--prim-gray-320);
	--color-code-gutter-foreground-active: var(--prim-gray-10);
	--color-code-indentation-marker: var(--prim-gray-740);
	--color-code-indentation-marker-active: var(--prim-gray-670);
	--color-line-break: var(--prim-gray-420);
	--color-code-line-break: var(--prim-color-secondary-tint-100);

	// Tag
	--tag-background-color: var(--prim-gray-670);
	--tag-background-hover-color: var(--prim-gray-540);
	--tag-border-color: var(--prim-gray-710);
	--tag-border-hover-color: var(--prim-gray-670);
	--tag-text-color: var(--color-text-dark);

	// Variables
	--color-variables-usage-font: var(--prim-color-alt-a-tint-300);
	--color-variables-usage-syntax-bg: var(--prim-color-alt-a-alpha-025);

	// Button primary
	--color-button-primary-focus-outline: var(--prim-color-primary-alpha-035);
	--color-button-primary-disabled-font: var(--prim-gray-0-alpha-030);
	--color-button-primary-disabled-border: transparent;
	--color-button-primary-disabled-background: var(--prim-color-primary-alpha-050);

	// Button secondary
	--color-button-secondary-font: var(--prim-gray-70);
	--color-button-secondary-border: var(--prim-gray-70);
	--color-button-secondary-background: transparent;
	--color-button-secondary-hover-active-focus-font: var(--prim-color-primary-tint-100);
	--color-button-secondary-hover-background: transparent;
	--color-button-secondary-active-focus-background: var(--prim-color-primary-alpha-010);
	--color-button-secondary-focus-outline: var(--prim-color-primary-alpha-035);
	--color-button-secondary-disabled-font: var(--prim-gray-0-alpha-030);
	--color-button-secondary-disabled-border: var(--prim-gray-0-alpha-030);

	// Button success, warning, danger
	--color-button-danger-font: var(--prim-gray-0);
	--color-button-danger-border: transparent;
	--color-button-danger-focus-outline: var(--prim-color-alt-c-tint-250);
	--color-button-danger-disabled-font: var(--prim-gray-0-alpha-025);
	--color-button-danger-disabled-border: transparent;
	--color-button-danger-disabled-background: var(--prim-color-alt-c-alpha-02);

	// Text button
	--color-text-button-secondary-font: var(--prim-gray-320);

	// Node Creator Button
	--color-button-node-creator-border-font: var(--color-button-secondary-font);
	--color-button-node-creator-hover-font: var(--color-button-secondary-hover-active-focus-font);
	--color-button-node-creator-hover-border: var(--prim-color-primary);
	--color-button-node-creator-background: var(--prim-color-primary-alpha-010);

	// Table
	--color-table-header-background: var(--prim-gray-740);
	--color-table-row-background: var(--prim-gray-820);
	--color-table-row-even-background: var(--prim-gray-800);
	--color-table-row-hover-background: var(--prim-gray-740);
	--color-table-row-highlight-background: var(--color-warning-tint-1);

	// Notification
	--color-notification-background: var(--prim-gray-740);

	// Execution
	--execution-card-background: var(--color-foreground-light);
	--execution-card-background-hover: var(--color-foreground-base);
	--execution-selector-background: var(--prim-gray-740);
	--execution-selector-text: var(--color-text-base);
	--execution-select-all-text: var(--color-text-base);
	--execution-card-text-waiting: var(--prim-color-secondary-tint-100);

	// NDV
	--color-run-data-background: var(--prim-gray-800);
	--color-ndv-droppable-parameter: var(--prim-color-primary);
	--color-ndv-droppable-parameter-background: var(--prim-color-primary-alpha-010);
	--color-ndv-droppable-parameter-active-background: var(--prim-color-alt-a-alpha-015);
	--color-ndv-back-font: var(--prim-gray-0);
	--color-ndv-overlay-background: var(--prim-color-alt-j-alpha-095);

	// Notice
	--color-notice-warning-border: var(--prim-color-alt-b-tint-250);
	--color-notice-warning-background: var(--prim-color-alt-b-alpha-02);
	--color-notice-font: var(--prim-gray-0);

	// Callout
	--color-callout-info-border: var(--prim-gray-670);
	--color-callout-info-background: var(--prim-gray-740);
	--color-callout-info-font: var(--prim-gray-0);
	--color-callout-success-border: var(--color-success);
	--color-callout-success-background: var(--prim-color-alt-a-shade-200);
	--color-callout-success-font: var(--prim-gray-0);
	--color-callout-warning-border: var(--color-warning);
	--color-callout-warning-background: var(--prim-color-alt-b-shade-350);
	--color-callout-warning-font: var(--prim-gray-0);
	--color-callout-danger-border: var(--color-danger);
	--color-callout-danger-background: var(--prim-color-alt-c-shade-250);
	--color-callout-danger-font: var(--prim-gray-0);
	--color-callout-danger-icon: var(--color-danger);
	--color-callout-secondary-border: var(--color-secondary);
	--color-callout-secondary-background: var(--prim-color-secondary-alpha-025);
	--color-callout-secondary-font: var(--prim-gray-0);

	// Dialogs and overlays
	--color-dialog-background: var(--prim-gray-800);
	--color-dialog-overlay-background: var(--prim-color-alt-j-alpha-075);
	--color-dialog-overlay-background-dark: var(--prim-color-alt-j-alpha-075);

	// Avatar
	--color-avatar-font: var(--prim-gray-0);

	// NPS Survey
	--color-nps-survey-background: var(--prim-gray-740);
	--color-nps-survey-font: var(--prim-gray-0);

	// Switch (Activation, boolean)
	--color-switch-background: var(--prim-gray-820);
	--color-switch-border-color: var(--prim-gray-670);
	--color-switch-toggle: var(--prim-gray-40);

	// Action Dropdown
	--color-action-dropdown-item-active-background: var(--color-background-xlight);

	// Input Triple
	--color-background-input-triple: var(--prim-gray-800);

	// Node error
	--color-node-error-output-text-color: var(--color-danger);

	// MFA Recovery codes
	--color-mfa-recovery-code-background: var(--color-background-xlight);
	--color-mfa-recovery-code-color: var(--color-text-dark);
	--color-mfa-lose-access-text-color: var(--color-danger);

	// Text highlight
	--color-text-highlight-background: var(--prim-color-alt-d-shade-600);

	// AI
	--node-type-background-l: 20%;
	--node-type-supplemental-label-color-h: 235;
	--node-type-supplemental-label-color-s: 28%;
	--node-type-supplemental-label-color-l: 40%;
	--node-type-supplemental-color-h: 235;
	--node-type-supplemental-color-s: 13%;
	--node-type-supplemental-color-l: 60%;
	--node-type-supplemental-label-color: hsl(
		var(--node-type-supplemental-label-color-h),
		var(--node-type-supplemental-label-color-s),
		var(--node-type-supplemental-label-color-l)
	);
	--node-type-supplemental-icon: var(--color-foreground-dark);
	--node-type-supplemental-color: hsl(
		var(--node-type-supplemental-color-h),
		var(--node-type-supplemental-color-s),
		var(--node-type-supplemental-color-l)
	);
	--node-type-supplemental-background: hsl(
		var(--node-type-supplemental-color-h),
		var(--node-type-supplemental-color-s),
		var(--node-type-background-l)
	);
	--node-type-supplemental-connector-color: var(--color-foreground-dark);
	--node-type-ai_chain-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_chain-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_chain-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_chain-color: hsl(
		var(--node-type-ai_chain-color-h),
		var(--node-type-ai_chain-color-s),
		var(--node-type-ai_chain-color-l)
	);
	--node-type-chain-background: hsl(
		var(--node-type-ai_chain-color-h),
		var(--node-type-ai_chain-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_document-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_document-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_document-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_document-color: hsl(
		var(--node-type-ai_document-color-h),
		var(--node-type-ai_document-color-s),
		var(--node-type-ai_document-color-l)
	);
	--node-type-ai_document-background: hsl(
		var(--node-type-ai_document-color-h),
		var(--node-type-ai_document-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_embedding-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_embedding-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_embedding-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_embedding-color: hsl(
		var(--node-type-ai_embedding-color-h),
		var(--node-type-ai_embedding-color-s),
		var(--node-type-ai_embedding-color-l)
	);
	--node-type-ai_embedding-background: hsl(
		var(--node-type-ai_embedding-color-h),
		var(--node-type-ai_embedding-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_languageModel-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_languageModel-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_languageModel-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_languageModel-color: hsl(
		var(--node-type-ai_languageModel-color-h),
		var(--node-type-ai_languageModel-color-s),
		var(--node-type-ai_languageModel-color-l)
	);
	--node-type-ai_languageModel-background: hsl(
		var(--node-type-ai_languageModel-color-h),
		var(--node-type-ai_languageModel-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_memory-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_memory-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_memory-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_memory-color: hsl(
		var(--node-type-ai_memory-color-h),
		var(--node-type-ai_memory-color-s),
		var(--node-type-ai_memory-color-l)
	);
	--node-type-ai_memory-background: hsl(
		var(--node-type-ai_memory-color-h),
		var(--node-type-ai_memory-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_outputParser-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_outputParser-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_outputParser-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_outputParser-color: hsl(
		var(--node-type-ai_outputParser-color-h),
		var(--node-type-ai_outputParser-color-s),
		var(--node-type-ai_outputParser-color-l)
	);
	--node-type-ai_outputParser-background: hsl(
		var(--node-type-ai_outputParser-color-h),
		var(--node-type-ai_outputParser-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_tool-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_tool-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_tool-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_tool-color: hsl(
		var(--node-type-ai_tool-color-h),
		var(--node-type-ai_tool-color-s),
		var(--node-type-ai_tool-color-l)
	);
	--node-type-ai_tool-background: hsl(
		var(--node-type-ai_tool-color-h),
		var(--node-type-ai_tool-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_retriever-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_retriever-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_retriever-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_retriever-color: hsl(
		var(--node-type-ai_retriever-color-h),
		var(--node-type-ai_retriever-color-s),
		var(--node-type-ai_retriever-color-l)
	);
	--node-type-ai_retriever-background: hsl(
		var(--node-type-ai_retriever-color-h),
		var(--node-type-ai_retriever-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_textSplitter-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_textSplitter-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_textSplitter-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_textSplitter-color: hsl(
		var(--node-type-ai_textSplitter-color-h),
		var(--node-type-ai_textSplitter-color-s),
		var(--node-type-ai_textSplitter-color-l)
	);
	--node-type-ai_textSplitter-background: hsl(
		var(--node-type-ai_textSplitter-color-h),
		var(--node-type-ai_textSplitter-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_vectorRetriever-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_vectorRetriever-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_vectorRetriever-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_vectorRetriever-color: hsl(
		var(--node-type-ai_vectorRetriever-color-h),
		var(--node-type-ai_vectorRetriever-color-s),
		var(--node-type-ai_vectorRetriever-color-l)
	);
	--node-type-ai_vectorRetriever-background: hsl(
		var(--node-type-ai_vectorRetriever-color-h),
		var(--node-type-ai_vectorRetriever-color-s),
		var(--node-type-background-l)
	);
	--node-type-ai_vectorStore-color-h: var(--node-type-supplemental-color-h);
	--node-type-ai_vectorStore-color-s: var(--node-type-supplemental-color-s);
	--node-type-ai_vectorStore-color-l: var(--node-type-supplemental-color-l);
	--node-type-ai_vectorStore-color: hsl(
		var(--node-type-ai_vectorStore-color-h),
		var(--node-type-ai_vectorStore-color-s),
		var(--node-type-ai_vectorStore-color-l)
	);
	--node-type-ai_vectorStore-background: hsl(
		var(--node-type-ai_vectorStore-color-h),
		var(--node-type-ai_vectorStore-color-s),
		var(--node-type-background-l)
	);

	// Various
	--color-info-tint-1: var(--prim-gray-420);
	--color-info-tint-2: var(--prim-gray-740);
	--border-color-base: var(--color-foreground-base);
	--border-color-light: var(--color-foreground-light);
	--border-base: var(--border-width-base) var(--border-style-base) var(--color-foreground-base);
	--node-type-supplemental-label-color-l: 100%;
	--node-type-supplemental-label-color: hsl(
		var(--node-type-supplemental-label-color-h),
		var(--node-type-supplemental-label-color-s),
		var(--node-type-supplemental-label-color-l)
	);
	--color-configurable-node-name: var(--color-text-dark);
	--color-secondary-link: var(--prim-color-secondary-tint-200);
	--color-secondary-link-hover: var(--prim-color-secondary-tint-100);
	//Params
	--color-icon-base: var(--color-text-light);
	--color-icon-hover: var(--prim-color-primary);

	--color-menu-background: var(--prim-gray-740);
	--color-menu-hover-background: var(--prim-gray-670);
	--color-menu-active-background: var(--prim-gray-670);
}

body[data-theme='dark'] {
	@include theme;
}

@media (prefers-color-scheme: dark) {
	body:not([data-theme]) {
		@include theme;
	}
}
