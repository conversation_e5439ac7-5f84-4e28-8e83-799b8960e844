// Primitive Colors
// Primitive colors shouldn't be used directly in components an other UI elements.
// They can be only mapped to tokens.
// Tokens should be used instead in components an other UI elements

@mixin primitives {
	// Gray
	--prim-gray-h: 220;

	--prim-gray-820: hsl(var(--prim-gray-h), 1%, 18%);
	--prim-gray-800: hsl(var(--prim-gray-h), 1%, 20%);
	--prim-gray-780: hsl(var(--prim-gray-h), 1%, 22%);
	--prim-gray-740: hsl(var(--prim-gray-h), 2%, 26%);
	--prim-gray-710: hsl(var(--prim-gray-h), 2%, 29%);
	--prim-gray-670: hsl(var(--prim-gray-h), 2%, 33%);
	--prim-gray-540: hsl(var(--prim-gray-h), 4%, 46%);
	--prim-gray-490: hsl(var(--prim-gray-h), 3%, 51%);
	--prim-gray-420: hsl(var(--prim-gray-h), 4%, 58%);
	--prim-gray-320: hsl(var(--prim-gray-h), 10%, 68%);
	--prim-gray-320-alpha-010: hsla(var(--prim-gray-h), 10%, 68%, 0.1);
	--prim-gray-200: hsl(var(--prim-gray-h), 18%, 80%);
	--prim-gray-120: hsl(var(--prim-gray-h), 25%, 88%);
	--prim-gray-70: hsl(var(--prim-gray-h), 32%, 93%);
	--prim-gray-70-alpha-01: hsla(var(--prim-gray-h), 32%, 93%, 0.1);
	--prim-gray-40: hsl(var(--prim-gray-h), 40%, 96%);
	--prim-gray-30: hsl(var(--prim-gray-h), 43%, 97%);
	--prim-gray-25: hsl(var(--prim-gray-h), 50%, 97.5%);
	--prim-gray-10: hsl(var(--prim-gray-h), 50%, 99%);
	--prim-gray-0-alpha-075: hsla(var(--prim-gray-h), 50%, 100%, 0.75);
	--prim-gray-0-alpha-030: hsla(var(--prim-gray-h), 50%, 100%, 0.3);
	--prim-gray-0-alpha-025: hsla(var(--prim-gray-h), 50%, 100%, 0.25);
	--prim-gray-0-alpha-010: hsla(var(--prim-gray-h), 50%, 100%, 0.1);
	--prim-gray-0: hsl(var(--prim-gray-h), 50%, 100%);

	// Color Primary
	--prim-color-primary-h: 7;
	--prim-color-primary-s: 100%;
	--prim-color-primary-l: 68%;

	--prim-color-primary-shade-300: hsl(
		var(--prim-color-primary-h),
		calc(var(--prim-color-primary-s) - 60%),
		calc(var(--prim-color-primary-l) - 30%)
	);
	--prim-color-primary-shade-100: hsl(
		var(--prim-color-primary-h),
		calc(var(--prim-color-primary-s) - 15%),
		calc(var(--prim-color-primary-l) - 10%)
	);
	--prim-color-primary: hsl(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		var(--prim-color-primary-l)
	);
	--prim-color-primary-alpha-010: hsla(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		var(--prim-color-primary-l),
		0.1
	);
	--prim-color-primary-alpha-035: hsla(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		var(--prim-color-primary-l),
		0.35
	);
	--prim-color-primary-alpha-050: hsla(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		var(--prim-color-primary-l),
		0.5
	);
	--prim-color-primary-tint-100: hsl(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		calc(var(--prim-color-primary-l) + 10%)
	);
	--prim-color-primary-tint-200: hsl(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		calc(var(--prim-color-primary-l) + 20%)
	);
	--prim-color-primary-tint-250: hsl(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		calc(var(--prim-color-primary-l) + 25%)
	);
	--prim-color-primary-tint-270: hsl(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		calc(var(--prim-color-primary-l) + 27%)
	);
	--prim-color-primary-tint-300: hsl(
		var(--prim-color-primary-h),
		var(--prim-color-primary-s),
		calc(var(--prim-color-primary-l) + 30%)
	);

	// Color Secondary
	--prim-color-secondary-h: 247;
	--prim-color-secondary-s: 49%;
	--prim-color-secondary-l: 53%;

	--prim-color-secondary-shade-250: hsl(
		var(--prim-color-secondary-h),
		calc(var(--prim-color-secondary-s) - 25%),
		calc(var(--prim-color-secondary-l) - 25%)
	);
	--prim-color-secondary-shade-100: hsl(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		calc(var(--prim-color-secondary-l) - 10%)
	);
	--prim-color-secondary: hsl(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		var(--prim-color-secondary-l)
	);
	--prim-color-secondary-alpha-025: hsla(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		var(--prim-color-secondary-l),
		0.25
	);
	--prim-color-secondary-alpha-010: hsla(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		var(--prim-color-secondary-l),
		0.1
	);
	--prim-color-secondary-tint-100: hsl(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		calc(var(--prim-color-secondary-l) + 10%)
	);
	--prim-color-secondary-tint-200: hsl(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		calc(var(--prim-color-secondary-l) + 20%)
	);
	--prim-color-secondary-tint-300: hsl(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		calc(var(--prim-color-secondary-l) + 30%)
	);
	--prim-color-secondary-tint-400: hsl(
		var(--prim-color-secondary-h),
		var(--prim-color-secondary-s),
		calc(var(--prim-color-secondary-l) + 40%)
	);

	// Color Alternate A
	--prim-color-alt-a-h: 150;
	--prim-color-alt-a-s: 60%;
	--prim-color-alt-a-l: 40%;

	--prim-color-alt-a-shade-200: hsl(
		var(--prim-color-alt-a-h),
		calc(var(--prim-color-alt-a-s) - 15%),
		calc(var(--prim-color-alt-a-l) - 20%)
	);
	--prim-color-alt-a-shade-100: hsl(
		var(--prim-color-alt-a-h),
		calc(var(--prim-color-alt-a-s) - 15%),
		calc(var(--prim-color-alt-a-l) - 10%)
	);
	--prim-color-alt-a: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		var(--prim-color-alt-a-l)
	);
	--prim-color-alt-a-alpha-025: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		var(--prim-color-alt-a-l),
		0.25
	);
	--prim-color-alt-a-alpha-015: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		var(--prim-color-alt-a-l),
		0.15
	);
	--prim-color-alt-a-tint-300: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		calc(var(--prim-color-alt-a-l) + 30%)
	);
	--prim-color-alt-a-tint-400: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		calc(var(--prim-color-alt-a-l) + 40%)
	);
	--prim-color-alt-a-tint-500: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		calc(var(--prim-color-alt-a-l) + 50%)
	);
	--prim-color-alt-a-tint-550: hsl(
		var(--prim-color-alt-a-h),
		var(--prim-color-alt-a-s),
		calc(var(--prim-color-alt-a-l) + 55%)
	);

	// Color Alternate B
	--prim-color-alt-b-h: 36;
	--prim-color-alt-b-s: 77%;
	--prim-color-alt-b-l: 57%;

	--prim-color-alt-b-shade-350: hsl(
		var(--prim-color-alt-b-h),
		calc(var(--prim-color-alt-b-s) - 35%),
		calc(var(--prim-color-alt-b-l) - 35%)
	);
	--prim-color-alt-b-shade-250: hsl(
		var(--prim-color-alt-b-h),
		calc(var(--prim-color-alt-b-s) - 35%),
		calc(var(--prim-color-alt-b-l) - 25%)
	);
	--prim-color-alt-b-shade-100: hsl(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		calc(var(--prim-color-alt-b-l) - 10%)
	);
	--prim-color-alt-b: hsl(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		var(--prim-color-alt-b-l)
	);
	--prim-color-alt-b-alpha-02: hsla(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		var(--prim-color-alt-b-l),
		0.2
	);
	--prim-color-alt-b-tint-150: hsl(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		calc(var(--prim-color-alt-b-l) + 15%)
	);
	--prim-color-alt-b-tint-250: hsl(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		calc(var(--prim-color-alt-b-l) + 25%)
	);
	--prim-color-alt-b-tint-300: hsl(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		calc(var(--prim-color-alt-b-l) + 30%)
	);
	--prim-color-alt-b-tint-400: hsl(
		var(--prim-color-alt-b-h),
		var(--prim-color-alt-b-s),
		calc(var(--prim-color-alt-b-l) + 40%)
	);

	// Color Alternate C
	--prim-color-alt-c-h: 355;
	--prim-color-alt-c-s: 83%;
	--prim-color-alt-c-l: 52%;

	--prim-color-alt-c-shade-250: hsl(
		var(--prim-color-alt-c-h),
		calc(var(--prim-color-alt-c-s) - 40%),
		calc(var(--prim-color-alt-c-l) - 25%)
	);
	--prim-color-alt-c-shade-150: hsl(
		var(--prim-color-alt-c-h),
		calc(var(--prim-color-alt-c-s) - 40%),
		calc(var(--prim-color-alt-c-l) - 15%)
	);
	--prim-color-alt-c-shade-100: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		calc(var(--prim-color-alt-c-l) - 10%)
	);
	--prim-color-alt-c: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		var(--prim-color-alt-c-l)
	);
	--prim-color-alt-c-alpha-02: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		var(--prim-color-alt-c-l),
		0.2
	);
	--prim-color-alt-c-tint-150: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		calc(var(--prim-color-alt-c-l) + 15%)
	);
	--prim-color-alt-c-tint-250: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		calc(var(--prim-color-alt-c-l) + 25%)
	);
	--prim-color-alt-c-tint-300: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		calc(var(--prim-color-alt-c-l) + 30%)
	);
	--prim-color-alt-c-tint-400: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		calc(var(--prim-color-alt-c-l) + 40%)
	);
	--prim-color-alt-c-tint-450: hsl(
		var(--prim-color-alt-c-h),
		var(--prim-color-alt-c-s),
		calc(var(--prim-color-alt-c-l) + 45%)
	);

	// Color Alternate D
	--prim-color-alt-d-h: 46;
	--prim-color-alt-d-s: 100%;
	--prim-color-alt-d-l: 92%;

	--prim-color-alt-d-shade-700: hsl(
		var(--prim-color-alt-d-h),
		calc(var(--prim-color-alt-d-s) - 55%),
		calc(var(--prim-color-alt-d-l) - 70%)
	);
	--prim-color-alt-d-shade-600: hsl(
		var(--prim-color-alt-d-h),
		calc(var(--prim-color-alt-d-s) - 55%),
		calc(var(--prim-color-alt-d-l) - 60%)
	);
	--prim-color-alt-d-shade-150: hsl(
		var(--prim-color-alt-d-h),
		var(--prim-color-alt-d-s),
		calc(var(--prim-color-alt-d-l) - 15%)
	);
	--prim-color-alt-d: hsl(
		var(--prim-color-alt-d-h),
		var(--prim-color-alt-d-s),
		var(--prim-color-alt-d-l)
	);

	// Color Alternate E
	--prim-color-alt-e-h: 210;
	--prim-color-alt-e-s: 67%;
	--prim-color-alt-e-l: 57%;

	--prim-color-alt-e-shade-350: hsl(
		var(--prim-color-alt-e-h),
		calc(var(--prim-color-alt-e-s) - 20%),
		calc(var(--prim-color-alt-e-l) - 35%)
	);
	--prim-color-alt-e-shade-250: hsl(
		var(--prim-color-alt-e-h),
		calc(var(--prim-color-alt-e-s) - 20%),
		calc(var(--prim-color-alt-e-l) - 25%)
	);
	--prim-color-alt-e-shade-150: hsl(
		var(--prim-color-alt-e-h),
		var(--prim-color-alt-e-s),
		calc(var(--prim-color-alt-e-l) - 15%)
	);
	--prim-color-alt-e-shade-100: hsl(
		var(--prim-color-alt-e-h),
		var(--prim-color-alt-e-s),
		calc(var(--prim-color-alt-e-l) - 10%)
	);
	--prim-color-alt-e: hsl(
		var(--prim-color-alt-e-h),
		var(--prim-color-alt-e-s),
		var(--prim-color-alt-e-l)
	);
	--prim-color-alt-e-alpha-04: hsla(
		var(--prim-color-alt-e-h),
		var(--prim-color-alt-e-s),
		var(--prim-color-alt-e-l),
		0.4
	);
	--prim-color-alt-e-tint-250: hsl(
		var(--prim-color-alt-e-h),
		var(--prim-color-alt-e-s),
		calc(var(--prim-color-alt-e-l) + 25%)
	);
	--prim-color-alt-e-tint-350: hsl(
		var(--prim-color-alt-e-h),
		var(--prim-color-alt-e-s),
		calc(var(--prim-color-alt-e-l) + 35%)
	);

	// Color Alternate F
	--prim-color-alt-f-h: 72;
	--prim-color-alt-f-s: 100%;
	--prim-color-alt-f-l: 27%;

	--prim-color-alt-f: hsl(
		var(--prim-color-alt-f-h),
		var(--prim-color-alt-f-s),
		var(--prim-color-alt-f-l)
	);

	--prim-color-alt-f-tint-150: hsl(
		var(--prim-color-alt-f-h),
		var(--prim-color-alt-f-s),
		calc(var(--prim-color-alt-f-l) + 15%)
	);

	// Color Alternate G
	--prim-color-alt-g-h: 276;
	--prim-color-alt-g-s: 31%;
	--prim-color-alt-g-l: 50%;

	--prim-color-alt-g: hsl(
		var(--prim-color-alt-g-h),
		var(--prim-color-alt-g-s),
		var(--prim-color-alt-g-l)
	);

	--prim-color-alt-g-tint-150: hsl(
		var(--prim-color-alt-g-h),
		var(--prim-color-alt-g-s),
		calc(var(--prim-color-alt-g-l) + 15%)
	);

	// Color Alternate H
	--prim-color-alt-h-h: 184;
	--prim-color-alt-h-s: 44%;
	--prim-color-alt-h-l: 43%;

	--prim-color-alt-h: hsl(
		var(--prim-color-alt-h-h),
		var(--prim-color-alt-h-s),
		var(--prim-color-alt-h-l)
	);

	// Color Alternate I
	--prim-color-alt-i-h: 147;
	--prim-color-alt-i-s: 83%;
	--prim-color-alt-i-l: 44%;

	--prim-color-alt-i: hsl(
		var(--prim-color-alt-i-h),
		var(--prim-color-alt-i-s),
		var(--prim-color-alt-i-l)
	);

	// Color Alternate J
	--prim-color-alt-j-h: 247;
	--prim-color-alt-j-s: 10%;
	--prim-color-alt-j-l: 30%;

	--prim-color-alt-j: hsl(
		var(--prim-color-alt-j-h),
		var(--prim-color-alt-j-s),
		var(--prim-color-alt-j-l)
	);
	--prim-color-alt-j-alpha-075: hsla(
		var(--prim-color-alt-j-h),
		var(--prim-color-alt-j-s),
		var(--prim-color-alt-j-l),
		0.75
	);

	--prim-color-alt-j-alpha-095: hsla(
		var(--prim-color-alt-j-h),
		var(--prim-color-alt-j-s),
		var(--prim-color-alt-j-l),
		0.95
	);

	// Color Alternate K - Used for errors in dark mode
	--prim-color-alt-k-h: 355;
	--prim-color-alt-k-s: 100%;
	--prim-color-alt-k-l: 69%;

	--prim-color-alt-k: hsl(
		var(--prim-color-alt-k-h),
		var(--prim-color-alt-k-s),
		var(--prim-color-alt-k-l)
	);

	--prim-color-white: hsl(0, 0%, 100%);
}

:root {
	@include primitives;
}
