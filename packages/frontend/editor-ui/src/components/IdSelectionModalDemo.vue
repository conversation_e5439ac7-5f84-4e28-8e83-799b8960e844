<script setup lang="ts">
import { ref } from 'vue';
import IdSelectionModal from './IdSelectionModal.vue';
import { N8nButton } from '@n8n/design-system';

const showModal = ref(false);
const selectedId = ref('');

const idOptions = [
	{ value: 'workflow-001', label: 'Workflow 001 - Customer Onboarding' },
	{ value: 'workflow-002', label: 'Workflow 002 - Data Processing' },
	{ value: 'workflow-003', label: 'Workflow 003 - Email Campaign' },
	{ value: 'workflow-004', label: 'Workflow 004 - Report Generation' },
];

const openModal = () => {
	showModal.value = true;
};

const onConfirm = (id: string) => {
	selectedId.value = id;
	showModal.value = false;
	console.log('Selected ID:', id);
};

const onCancel = () => {
	showModal.value = false;
	console.log('Modal cancelled');
};
</script>

<template>
	<div class="demo-container">
		<h2>ID Selection Modal Demo</h2>
		<p>This demo shows the ID selection modal that appears when pressing Ctrl+S to save a workflow.</p>
		
		<div class="demo-section">
			<N8nButton type="primary" @click="openModal">
				Open ID Selection Modal
			</N8nButton>
		</div>

		<div v-if="selectedId" class="result-section">
			<h3>Selected ID:</h3>
			<p><strong>{{ selectedId }}</strong></p>
		</div>

		<IdSelectionModal
			v-model="showModal"
			title="Select Workflow ID"
			description="Please select an ID for this workflow before saving:"
			:id-options="idOptions"
			@confirm="onConfirm"
			@cancel="onCancel"
		/>
	</div>
</template>

<style scoped>
.demo-container {
	padding: 20px;
	max-width: 600px;
	margin: 0 auto;
}

.demo-section {
	margin: 20px 0;
}

.result-section {
	margin-top: 20px;
	padding: 15px;
	background-color: #f5f5f5;
	border-radius: 4px;
}

.result-section h3 {
	margin-top: 0;
	color: #333;
}

.result-section p {
	margin-bottom: 0;
	color: #666;
}
</style>
