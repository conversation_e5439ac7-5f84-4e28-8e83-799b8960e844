<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from '@n8n/i18n';
import { N8nButton, N8nSelect, N8nOption } from '@n8n/design-system';

interface Props {
	modelValue: boolean;
	title?: string;
	description?: string;
	idOptions: Array<{ value: string; label: string }>;
	selectedId?: string;
}

interface Emits {
	(event: 'update:modelValue', value: boolean): void;
	(event: 'confirm', selectedId: string): void;
	(event: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
	title: 'Select ID',
	description: 'Please select an ID for this workflow:',
});

const emit = defineEmits<Emits>();

const i18n = useI18n();
const selectedId = ref(props.selectedId || '');

const isValid = computed(() => selectedId.value !== '');

const onConfirm = () => {
	if (isValid.value) {
		emit('confirm', selectedId.value);
		emit('update:modelValue', false);
	}
};

const onCancel = () => {
	emit('cancel');
	emit('update:modelValue', false);
};

const onModalClose = () => {
	emit('update:modelValue', false);
};
</script>

<template>
	<N8nModal
		:model-value="modelValue"
		:title="title"
		:center="true"
		width="500px"
		@update:model-value="onModalClose"
	>
		<template #content>
			<div class="id-selection-content">
				<p class="description">{{ description }}</p>
				<div class="select-container">
					<N8nSelect
						v-model="selectedId"
						:placeholder="选择项目"
						size="large"
						data-test-id="id-selection-select"
					>
						<N8nOption
							v-for="option in idOptions"
							:key="option.value"
							:value="option.value"
							:label="option.label"
						/>
					</N8nSelect>
				</div>
			</div>
		</template>
		<template #footer>
			<div class="modal-footer">
				<N8nButton type="secondary" @click="onCancel">
					{{ i18n.baseText('generic.cancel') }}
				</N8nButton>
				<N8nButton type="primary" :disabled="!isValid" @click="onConfirm">
					{{ i18n.baseText('generic.confirm') }}
				</N8nButton>
			</div>
		</template>
	</N8nModal>
</template>

<style scoped>
.id-selection-content {
	padding: 0 0 20px 0;
}

.description {
	margin-bottom: 20px;
	color: var(--color-text-base);
	font-size: 14px;
	line-height: 1.5;
}

.select-container {
	margin-bottom: 20px;
}

.modal-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}
</style>
