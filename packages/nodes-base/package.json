{"name": "n8n-nodes-base", "version": "1.101.0", "description": "Base nodes of n8n", "main": "index.js", "scripts": {"clean": "rimraf dist .turbo", "copy-nodes-json": "node scripts/copy-nodes-json.js .", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc --build tsconfig.build.cjs.json && pnpm copy-nodes-json && tsc-alias -p tsconfig.build.cjs.json && pnpm n8n-copy-static-files && pnpm n8n-generate-translations && pnpm n8n-generate-metadata", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint nodes credentials utils test --quiet && node ./scripts/validate-load-options-methods.js", "lintfix": "eslint nodes credentials utils test --fix", "watch": "tsc-watch -p tsconfig.build.cjs.json --onCompilationComplete \"pnpm copy-nodes-json && tsc-alias -p tsconfig.build.cjs.json\" --onSuccess \"pnpm n8n-generate-metadata\"", "test": "jest", "test:dev": "jest --watch"}, "files": ["dist"], "n8n": {"credentials": ["dist/credentials/ActionNetworkApi.credentials.js", "dist/credentials/ActiveCampaignApi.credentials.js", "dist/credentials/AcuitySchedulingApi.credentials.js", "dist/credentials/AcuitySchedulingOAuth2Api.credentials.js", "dist/credentials/AdaloApi.credentials.js", "dist/credentials/AffinityApi.credentials.js", "dist/credentials/AgileCrmApi.credentials.js", "dist/credentials/AirtableApi.credentials.js", "dist/credentials/AirtableOAuth2Api.credentials.js", "dist/credentials/AirtableTokenApi.credentials.js", "dist/credentials/AirtopApi.credentials.js", "dist/credentials/AlienVaultApi.credentials.js", "dist/credentials/Amqp.credentials.js", "dist/credentials/ApiTemplateIoApi.credentials.js", "dist/credentials/AsanaApi.credentials.js", "dist/credentials/AsanaOAuth2Api.credentials.js", "dist/credentials/Auth0ManagementApi.credentials.js", "dist/credentials/AutomizyApi.credentials.js", "dist/credentials/AutopilotApi.credentials.js", "dist/credentials/Aws.credentials.js", "dist/credentials/AzureStorageOAuth2Api.credentials.js", "dist/credentials/AzureStorageSharedKeyApi.credentials.js", "dist/credentials/BambooHrApi.credentials.js", "dist/credentials/BannerbearApi.credentials.js", "dist/credentials/BaserowApi.credentials.js", "dist/credentials/BeeminderApi.credentials.js", "dist/credentials/BitbucketApi.credentials.js", "dist/credentials/BitlyApi.credentials.js", "dist/credentials/BitlyOAuth2Api.credentials.js", "dist/credentials/BitwardenApi.credentials.js", "dist/credentials/BoxOAuth2Api.credentials.js", "dist/credentials/BrandfetchApi.credentials.js", "dist/credentials/BubbleApi.credentials.js", "dist/credentials/CalApi.credentials.js", "dist/credentials/CalendlyApi.credentials.js", "dist/credentials/CalendlyOAuth2Api.credentials.js", "dist/credentials/CarbonBlackApi.credentials.js", "dist/credentials/ChargebeeApi.credentials.js", "dist/credentials/CircleCiApi.credentials.js", "dist/credentials/CiscoMerakiApi.credentials.js", "dist/credentials/CiscoSecureEndpointApi.credentials.js", "dist/credentials/CiscoWebexOAuth2Api.credentials.js", "dist/credentials/CiscoUmbrellaApi.credentials.js", "dist/credentials/CloudflareApi.credentials.js", "dist/credentials/ClearbitApi.credentials.js", "dist/credentials/ClickUpApi.credentials.js", "dist/credentials/ClickUpOAuth2Api.credentials.js", "dist/credentials/ClockifyApi.credentials.js", "dist/credentials/CockpitApi.credentials.js", "dist/credentials/CodaApi.credentials.js", "dist/credentials/ContentfulApi.credentials.js", "dist/credentials/ConvertApi.credentials.js", "dist/credentials/ConvertKitApi.credentials.js", "dist/credentials/CopperApi.credentials.js", "dist/credentials/CortexApi.credentials.js", "dist/credentials/CrateDb.credentials.js", "dist/credentials/CrowdStrikeOAuth2Api.credentials.js", "dist/credentials/CrowdDevApi.credentials.js", "dist/credentials/CustomerIoApi.credentials.js", "dist/credentials/DatadogApi.credentials.js", "dist/credentials/DeepLApi.credentials.js", "dist/credentials/DemioApi.credentials.js", "dist/credentials/DfirIrisApi.credentials.js", "dist/credentials/DhlApi.credentials.js", "dist/credentials/DiscordBotApi.credentials.js", "dist/credentials/DiscordOAuth2Api.credentials.js", "dist/credentials/DiscordWebhookApi.credentials.js", "dist/credentials/DiscourseApi.credentials.js", "dist/credentials/DisqusApi.credentials.js", "dist/credentials/DriftApi.credentials.js", "dist/credentials/DriftOAuth2Api.credentials.js", "dist/credentials/DropboxApi.credentials.js", "dist/credentials/DropboxOAuth2Api.credentials.js", "dist/credentials/DropcontactApi.credentials.js", "dist/credentials/DynatraceApi.credentials.js", "dist/credentials/EgoiApi.credentials.js", "dist/credentials/ElasticsearchApi.credentials.js", "dist/credentials/ElasticSecurityApi.credentials.js", "dist/credentials/EmeliaApi.credentials.js", "dist/credentials/ERPNextApi.credentials.js", "dist/credentials/EventbriteApi.credentials.js", "dist/credentials/EventbriteOAuth2Api.credentials.js", "dist/credentials/F5BigIpApi.credentials.js", "dist/credentials/FacebookGraphApi.credentials.js", "dist/credentials/FacebookGraphAppApi.credentials.js", "dist/credentials/FacebookLeadAdsOAuth2Api.credentials.js", "dist/credentials/FigmaApi.credentials.js", "dist/credentials/FileMaker.credentials.js", "dist/credentials/FilescanApi.credentials.js", "dist/credentials/FlowApi.credentials.js", "dist/credentials/FormIoApi.credentials.js", "dist/credentials/FormstackApi.credentials.js", "dist/credentials/FormstackOAuth2Api.credentials.js", "dist/credentials/FortiGateApi.credentials.js", "dist/credentials/FreshdeskApi.credentials.js", "dist/credentials/FreshserviceApi.credentials.js", "dist/credentials/FreshworksCrmApi.credentials.js", "dist/credentials/Ftp.credentials.js", "dist/credentials/GetResponseApi.credentials.js", "dist/credentials/GetResponseOAuth2Api.credentials.js", "dist/credentials/GhostAdminApi.credentials.js", "dist/credentials/GhostContentApi.credentials.js", "dist/credentials/GithubApi.credentials.js", "dist/credentials/GithubOAuth2Api.credentials.js", "dist/credentials/GitlabApi.credentials.js", "dist/credentials/GitlabOAuth2Api.credentials.js", "dist/credentials/GitPassword.credentials.js", "dist/credentials/GmailOAuth2Api.credentials.js", "dist/credentials/GongApi.credentials.js", "dist/credentials/GongOAuth2Api.credentials.js", "dist/credentials/GoogleAdsOAuth2Api.credentials.js", "dist/credentials/GoogleAnalyticsOAuth2Api.credentials.js", "dist/credentials/GoogleApi.credentials.js", "dist/credentials/GoogleBigQueryOAuth2Api.credentials.js", "dist/credentials/GoogleBooksOAuth2Api.credentials.js", "dist/credentials/GoogleCalendarOAuth2Api.credentials.js", "dist/credentials/GoogleChatOAuth2Api.credentials.js", "dist/credentials/GoogleCloudNaturalLanguageOAuth2Api.credentials.js", "dist/credentials/GoogleCloudStorageOAuth2Api.credentials.js", "dist/credentials/GoogleContactsOAuth2Api.credentials.js", "dist/credentials/GoogleDocsOAuth2Api.credentials.js", "dist/credentials/GoogleDriveOAuth2Api.credentials.js", "dist/credentials/GoogleFirebaseCloudFirestoreOAuth2Api.credentials.js", "dist/credentials/GoogleFirebaseRealtimeDatabaseOAuth2Api.credentials.js", "dist/credentials/GoogleBusinessProfileOAuth2Api.credentials.js", "dist/credentials/GoogleOAuth2Api.credentials.js", "dist/credentials/GooglePerspectiveOAuth2Api.credentials.js", "dist/credentials/GoogleSheetsOAuth2Api.credentials.js", "dist/credentials/GoogleSheetsTriggerOAuth2Api.credentials.js", "dist/credentials/GoogleSlidesOAuth2Api.credentials.js", "dist/credentials/GoogleTasksOAuth2Api.credentials.js", "dist/credentials/GoogleTranslateOAuth2Api.credentials.js", "dist/credentials/GotifyApi.credentials.js", "dist/credentials/GoToWebinarOAuth2Api.credentials.js", "dist/credentials/GristApi.credentials.js", "dist/credentials/GrafanaApi.credentials.js", "dist/credentials/GSuiteAdminOAuth2Api.credentials.js", "dist/credentials/GumroadApi.credentials.js", "dist/credentials/HaloPSAApi.credentials.js", "dist/credentials/HarvestApi.credentials.js", "dist/credentials/HarvestOAuth2Api.credentials.js", "dist/credentials/HelpScoutOAuth2Api.credentials.js", "dist/credentials/HighLevelApi.credentials.js", "dist/credentials/HighLevelOAuth2Api.credentials.js", "dist/credentials/HomeAssistantApi.credentials.js", "dist/credentials/HttpBasicAuth.credentials.js", "dist/credentials/HttpBearerAuth.credentials.js", "dist/credentials/HttpDigestAuth.credentials.js", "dist/credentials/HttpHeaderAuth.credentials.js", "dist/credentials/HttpCustomAuth.credentials.js", "dist/credentials/HttpQueryAuth.credentials.js", "dist/credentials/HttpSslAuth.credentials.js", "dist/credentials/HubspotApi.credentials.js", "dist/credentials/HubspotAppToken.credentials.js", "dist/credentials/HubspotDeveloperApi.credentials.js", "dist/credentials/HubspotOAuth2Api.credentials.js", "dist/credentials/HumanticAiApi.credentials.js", "dist/credentials/HunterApi.credentials.js", "dist/credentials/HybridAnalysisApi.credentials.js", "dist/credentials/Imap.credentials.js", "dist/credentials/ImpervaWafApi.credentials.js", "dist/credentials/IntercomApi.credentials.js", "dist/credentials/InvoiceNinjaApi.credentials.js", "dist/credentials/IterableApi.credentials.js", "dist/credentials/JenkinsApi.credentials.js", "dist/credentials/JinaAiApi.credentials.js", "dist/credentials/JiraSoftwareCloudApi.credentials.js", "dist/credentials/JiraSoftwareServerApi.credentials.js", "dist/credentials/JiraSoftwareServerPatApi.credentials.js", "dist/credentials/JotFormApi.credentials.js", "dist/credentials/JwtAuth.credentials.js", "dist/credentials/Kafka.credentials.js", "dist/credentials/KeapOAuth2Api.credentials.js", "dist/credentials/KibanaApi.credentials.js", "dist/credentials/KitemakerApi.credentials.js", "dist/credentials/KoBoToolboxApi.credentials.js", "dist/credentials/Ldap.credentials.js", "dist/credentials/LemlistApi.credentials.js", "dist/credentials/LinearApi.credentials.js", "dist/credentials/LinearOAuth2Api.credentials.js", "dist/credentials/LineNotifyOAuth2Api.credentials.js", "dist/credentials/LingvaNexApi.credentials.js", "dist/credentials/LinkedInCommunityManagementOAuth2Api.credentials.js", "dist/credentials/LinkedInOAuth2Api.credentials.js", "dist/credentials/LoneScaleApi.credentials.js", "dist/credentials/Magento2Api.credentials.js", "dist/credentials/MailcheckApi.credentials.js", "dist/credentials/MailchimpApi.credentials.js", "dist/credentials/MailchimpOAuth2Api.credentials.js", "dist/credentials/MailerLiteApi.credentials.js", "dist/credentials/MailgunApi.credentials.js", "dist/credentials/MailjetEmailApi.credentials.js", "dist/credentials/MailjetSmsApi.credentials.js", "dist/credentials/MalcoreApi.credentials.js", "dist/credentials/MandrillApi.credentials.js", "dist/credentials/MarketstackApi.credentials.js", "dist/credentials/MatrixApi.credentials.js", "dist/credentials/MattermostApi.credentials.js", "dist/credentials/MauticApi.credentials.js", "dist/credentials/MauticOAuth2Api.credentials.js", "dist/credentials/MediumApi.credentials.js", "dist/credentials/MediumOAuth2Api.credentials.js", "dist/credentials/MetabaseApi.credentials.js", "dist/credentials/MessageBirdApi.credentials.js", "dist/credentials/MetabaseApi.credentials.js", "dist/credentials/MicrosoftAzureCosmosDbSharedKeyApi.credentials.js", "dist/credentials/MicrosoftAzureMonitorOAuth2Api.credentials.js", "dist/credentials/MicrosoftDynamicsOAuth2Api.credentials.js", "dist/credentials/MicrosoftEntraOAuth2Api.credentials.js", "dist/credentials/MicrosoftExcelOAuth2Api.credentials.js", "dist/credentials/MicrosoftGraphSecurityOAuth2Api.credentials.js", "dist/credentials/MicrosoftOAuth2Api.credentials.js", "dist/credentials/MicrosoftOneDriveOAuth2Api.credentials.js", "dist/credentials/MicrosoftOutlookOAuth2Api.credentials.js", "dist/credentials/MicrosoftSharePointOAuth2Api.credentials.js", "dist/credentials/MicrosoftSql.credentials.js", "dist/credentials/MicrosoftTeamsOAuth2Api.credentials.js", "dist/credentials/MicrosoftToDoOAuth2Api.credentials.js", "dist/credentials/MindeeInvoiceApi.credentials.js", "dist/credentials/MindeeReceiptApi.credentials.js", "dist/credentials/MiroOAuth2Api.credentials.js", "dist/credentials/MispApi.credentials.js", "dist/credentials/MistApi.credentials.js", "dist/credentials/MoceanApi.credentials.js", "dist/credentials/MondayComApi.credentials.js", "dist/credentials/MondayComOAuth2Api.credentials.js", "dist/credentials/MongoDb.credentials.js", "dist/credentials/MonicaCrmApi.credentials.js", "dist/credentials/Mqtt.credentials.js", "dist/credentials/Msg91Api.credentials.js", "dist/credentials/MySql.credentials.js", "dist/credentials/N8nApi.credentials.js", "dist/credentials/NasaApi.credentials.js", "dist/credentials/NetlifyApi.credentials.js", "dist/credentials/NetscalerAdcApi.credentials.js", "dist/credentials/NextCloudApi.credentials.js", "dist/credentials/NextCloudOAuth2Api.credentials.js", "dist/credentials/NocoDb.credentials.js", "dist/credentials/NocoDbApiToken.credentials.js", "dist/credentials/NotionApi.credentials.js", "dist/credentials/NotionOAuth2Api.credentials.js", "dist/credentials/NpmApi.credentials.js", "dist/credentials/OAuth1Api.credentials.js", "dist/credentials/OAuth2Api.credentials.js", "dist/credentials/OdooApi.credentials.js", "dist/credentials/OktaApi.credentials.js", "dist/credentials/OneSimpleApi.credentials.js", "dist/credentials/OnfleetApi.credentials.js", "dist/credentials/OpenAiApi.credentials.js", "dist/credentials/OpenCTIApi.credentials.js", "dist/credentials/OpenWeatherMapApi.credentials.js", "dist/credentials/OrbitApi.credentials.js", "dist/credentials/OuraApi.credentials.js", "dist/credentials/PaddleApi.credentials.js", "dist/credentials/PagerDutyApi.credentials.js", "dist/credentials/PagerDutyOAuth2Api.credentials.js", "dist/credentials/PayPalApi.credentials.js", "dist/credentials/PeekalinkApi.credentials.js", "dist/credentials/PerplexityApi.credentials.js", "dist/credentials/PhantombusterApi.credentials.js", "dist/credentials/PhilipsHueOAuth2Api.credentials.js", "dist/credentials/PipedriveApi.credentials.js", "dist/credentials/PipedriveOAuth2Api.credentials.js", "dist/credentials/PlivoApi.credentials.js", "dist/credentials/Postgres.credentials.js", "dist/credentials/PostHogApi.credentials.js", "dist/credentials/PostmarkApi.credentials.js", "dist/credentials/ProfitWellApi.credentials.js", "dist/credentials/PushbulletOAuth2Api.credentials.js", "dist/credentials/PushcutApi.credentials.js", "dist/credentials/PushoverApi.credentials.js", "dist/credentials/QRadarApi.credentials.js", "dist/credentials/QualysApi.credentials.js", "dist/credentials/QuestDb.credentials.js", "dist/credentials/QuickBaseApi.credentials.js", "dist/credentials/QuickBooksOAuth2Api.credentials.js", "dist/credentials/RabbitMQ.credentials.js", "dist/credentials/RaindropOAuth2Api.credentials.js", "dist/credentials/Rapid7InsightVmApi.credentials.js", "dist/credentials/RecordedFutureApi.credentials.js", "dist/credentials/RedditOAuth2Api.credentials.js", "dist/credentials/Redis.credentials.js", "dist/credentials/RocketchatApi.credentials.js", "dist/credentials/RundeckApi.credentials.js", "dist/credentials/S3.credentials.js", "dist/credentials/SalesforceJwtApi.credentials.js", "dist/credentials/SalesforceOAuth2Api.credentials.js", "dist/credentials/SalesmateApi.credentials.js", "dist/credentials/SeaTableApi.credentials.js", "dist/credentials/SecurityScorecardApi.credentials.js", "dist/credentials/SegmentApi.credentials.js", "dist/credentials/SekoiaApi.credentials.js", "dist/credentials/SendGridApi.credentials.js", "dist/credentials/BrevoApi.credentials.js", "dist/credentials/SendyApi.credentials.js", "dist/credentials/SentryIoApi.credentials.js", "dist/credentials/SentryIoOAuth2Api.credentials.js", "dist/credentials/SentryIoServerApi.credentials.js", "dist/credentials/ServiceNowOAuth2Api.credentials.js", "dist/credentials/ServiceNowBasicApi.credentials.js", "dist/credentials/Sftp.credentials.js", "dist/credentials/ShopifyApi.credentials.js", "dist/credentials/ShopifyAccessTokenApi.credentials.js", "dist/credentials/ShopifyOAuth2Api.credentials.js", "dist/credentials/Signl4Api.credentials.js", "dist/credentials/SlackApi.credentials.js", "dist/credentials/SlackOAuth2Api.credentials.js", "dist/credentials/Sms77Api.credentials.js", "dist/credentials/Smtp.credentials.js", "dist/credentials/Snowflake.credentials.js", "dist/credentials/SolarWindsIpamApi.credentials.js", "dist/credentials/SolarWindsObservabilityApi.credentials.js", "dist/credentials/SplunkApi.credentials.js", "dist/credentials/SpontitApi.credentials.js", "dist/credentials/SpotifyOAuth2Api.credentials.js", "dist/credentials/ShufflerApi.credentials.js", "dist/credentials/SshPassword.credentials.js", "dist/credentials/SshPrivateKey.credentials.js", "dist/credentials/StackbyApi.credentials.js", "dist/credentials/StoryblokContentApi.credentials.js", "dist/credentials/StoryblokManagementApi.credentials.js", "dist/credentials/StrapiApi.credentials.js", "dist/credentials/StrapiTokenApi.credentials.js", "dist/credentials/StravaOAuth2Api.credentials.js", "dist/credentials/StripeApi.credentials.js", "dist/credentials/SupabaseApi.credentials.js", "dist/credentials/SurveyMonkeyApi.credentials.js", "dist/credentials/SurveyMonkeyOAuth2Api.credentials.js", "dist/credentials/SyncroMspApi.credentials.js", "dist/credentials/SysdigApi.credentials.js", "dist/credentials/TaigaApi.credentials.js", "dist/credentials/TapfiliateApi.credentials.js", "dist/credentials/TelegramApi.credentials.js", "dist/credentials/TheHiveProjectApi.credentials.js", "dist/credentials/TheHiveApi.credentials.js", "dist/credentials/TimescaleDb.credentials.js", "dist/credentials/TodoistApi.credentials.js", "dist/credentials/TodoistOAuth2Api.credentials.js", "dist/credentials/TogglApi.credentials.js", "dist/credentials/TotpApi.credentials.js", "dist/credentials/TravisCiApi.credentials.js", "dist/credentials/TrellixEpoApi.credentials.js", "dist/credentials/TrelloApi.credentials.js", "dist/credentials/TwakeCloudApi.credentials.js", "dist/credentials/TwakeServerApi.credentials.js", "dist/credentials/TwilioApi.credentials.js", "dist/credentials/TwistOAuth2Api.credentials.js", "dist/credentials/TwitterOAuth1Api.credentials.js", "dist/credentials/TwitterOAuth2Api.credentials.js", "dist/credentials/TypeformApi.credentials.js", "dist/credentials/TypeformOAuth2Api.credentials.js", "dist/credentials/UnleashedSoftwareApi.credentials.js", "dist/credentials/UpleadApi.credentials.js", "dist/credentials/UProcApi.credentials.js", "dist/credentials/UptimeRobotApi.credentials.js", "dist/credentials/UrlScanIoApi.credentials.js", "dist/credentials/VeroApi.credentials.js", "dist/credentials/VerticaApi.credentials.js", "dist/credentials/VirusTotalApi.credentials.js", "dist/credentials/VonageApi.credentials.js", "dist/credentials/VenafiTlsProtectCloudApi.credentials.js", "dist/credentials/VenafiTlsProtectDatacenterApi.credentials.js", "dist/credentials/WebflowApi.credentials.js", "dist/credentials/WebflowOAuth2Api.credentials.js", "dist/credentials/WekanApi.credentials.js", "dist/credentials/WhatsAppApi.credentials.js", "dist/credentials/WhatsAppTriggerApi.credentials.js", "dist/credentials/WiseApi.credentials.js", "dist/credentials/WooCommerceApi.credentials.js", "dist/credentials/WordpressApi.credentials.js", "dist/credentials/WorkableApi.credentials.js", "dist/credentials/WufooApi.credentials.js", "dist/credentials/XeroOAuth2Api.credentials.js", "dist/credentials/YourlsApi.credentials.js", "dist/credentials/YouTubeOAuth2Api.credentials.js", "dist/credentials/ZabbixApi.credentials.js", "dist/credentials/ZammadBasicAuthApi.credentials.js", "dist/credentials/ZammadTokenAuthApi.credentials.js", "dist/credentials/ZendeskApi.credentials.js", "dist/credentials/ZendeskOAuth2Api.credentials.js", "dist/credentials/ZohoOAuth2Api.credentials.js", "dist/credentials/ZoomApi.credentials.js", "dist/credentials/ZoomOAuth2Api.credentials.js", "dist/credentials/ZscalerZiaApi.credentials.js", "dist/credentials/ZulipApi.credentials.js"], "nodes": ["dist/nodes/ActionNetwork/ActionNetwork.node.js", "dist/nodes/ActiveCampaign/ActiveCampaign.node.js", "dist/nodes/ActiveCampaign/ActiveCampaignTrigger.node.js", "dist/nodes/AcuityScheduling/AcuitySchedulingTrigger.node.js", "dist/nodes/Adalo/Adalo.node.js", "dist/nodes/Affinity/Affinity.node.js", "dist/nodes/Affinity/AffinityTrigger.node.js", "dist/nodes/AgileCrm/AgileCrm.node.js", "dist/nodes/Airtable/Airtable.node.js", "dist/nodes/Airtable/AirtableTrigger.node.js", "dist/nodes/Airtop/Airtop.node.js", "dist/nodes/AiTransform/AiTransform.node.js", "dist/nodes/Amqp/Amqp.node.js", "dist/nodes/Amqp/AmqpTrigger.node.js", "dist/nodes/ApiTemplateIo/ApiTemplateIo.node.js", "dist/nodes/Asana/Asana.node.js", "dist/nodes/Asana/AsanaTrigger.node.js", "dist/nodes/Automizy/Automizy.node.js", "dist/nodes/Autopilot/Autopilot.node.js", "dist/nodes/Autopilot/AutopilotTrigger.node.js", "dist/nodes/Aws/AwsLambda.node.js", "dist/nodes/Aws/AwsSns.node.js", "dist/nodes/Aws/AwsSnsTrigger.node.js", "dist/nodes/Aws/CertificateManager/AwsCertificateManager.node.js", "dist/nodes/Aws/Cognito/AwsCognito.node.js", "dist/nodes/Aws/Comprehend/AwsComprehend.node.js", "dist/nodes/Aws/DynamoDB/AwsDynamoDB.node.js", "dist/nodes/Aws/ELB/AwsElb.node.js", "dist/nodes/Aws/IAM/AwsIam.node.js", "dist/nodes/Aws/Rekognition/AwsRekognition.node.js", "dist/nodes/Aws/S3/AwsS3.node.js", "dist/nodes/Aws/SES/AwsSes.node.js", "dist/nodes/Aws/SQS/AwsSqs.node.js", "dist/nodes/Aws/Textract/AwsTextract.node.js", "dist/nodes/Aws/Transcribe/AwsTranscribe.node.js", "dist/nodes/BambooHr/BambooHr.node.js", "dist/nodes/Bannerbear/Bannerbear.node.js", "dist/nodes/Baserow/Baserow.node.js", "dist/nodes/Beeminder/Beeminder.node.js", "dist/nodes/Bitbucket/BitbucketTrigger.node.js", "dist/nodes/Bitly/Bitly.node.js", "dist/nodes/Bitwarden/Bitwarden.node.js", "dist/nodes/Box/Box.node.js", "dist/nodes/Box/BoxTrigger.node.js", "dist/nodes/Brandfetch/Brandfetch.node.js", "dist/nodes/Bubble/Bubble.node.js", "dist/nodes/Cal/CalTrigger.node.js", "dist/nodes/Calendly/CalendlyTrigger.node.js", "dist/nodes/Chargebee/Chargebee.node.js", "dist/nodes/Chargebee/ChargebeeTrigger.node.js", "dist/nodes/CircleCi/CircleCi.node.js", "dist/nodes/Cisco/Webex/CiscoWebex.node.js", "dist/nodes/Cisco/Webex/CiscoWebexTrigger.node.js", "dist/nodes/Cloudflare/Cloudflare.node.js", "dist/nodes/Clearbit/Clearbit.node.js", "dist/nodes/ClickUp/ClickUp.node.js", "dist/nodes/ClickUp/ClickUpTrigger.node.js", "dist/nodes/Clockify/Clockify.node.js", "dist/nodes/Clockify/ClockifyTrigger.node.js", "dist/nodes/Cockpit/Cockpit.node.js", "dist/nodes/Coda/Coda.node.js", "dist/nodes/Code/Code.node.js", "dist/nodes/CoinGecko/CoinGecko.node.js", "dist/nodes/CompareDatasets/CompareDatasets.node.js", "dist/nodes/Compression/Compression.node.js", "dist/nodes/Contentful/Contentful.node.js", "dist/nodes/ConvertKit/ConvertKit.node.js", "dist/nodes/ConvertKit/ConvertKitTrigger.node.js", "dist/nodes/Copper/Copper.node.js", "dist/nodes/Copper/CopperTrigger.node.js", "dist/nodes/Cortex/Cortex.node.js", "dist/nodes/CrateDb/CrateDb.node.js", "dist/nodes/Cron/Cron.node.js", "dist/nodes/CrowdDev/CrowdDev.node.js", "dist/nodes/CrowdDev/CrowdDevTrigger.node.js", "dist/nodes/Crypto/Crypto.node.js", "dist/nodes/CustomerIo/CustomerIo.node.js", "dist/nodes/CustomerIo/CustomerIoTrigger.node.js", "dist/nodes/DateTime/DateTime.node.js", "dist/nodes/DebugHelper/DebugHelper.node.js", "dist/nodes/DeepL/DeepL.node.js", "dist/nodes/Demio/Demio.node.js", "dist/nodes/Dhl/Dhl.node.js", "dist/nodes/Discord/Discord.node.js", "dist/nodes/Discourse/Discourse.node.js", "dist/nodes/Disqus/Disqus.node.js", "dist/nodes/Drift/Drift.node.js", "dist/nodes/Dropbox/Dropbox.node.js", "dist/nodes/Dropcontact/Dropcontact.node.js", "dist/nodes/EditImage/EditImage.node.js", "dist/nodes/E2eTest/E2eTest.node.js", "dist/nodes/Egoi/Egoi.node.js", "dist/nodes/Elastic/Elasticsearch/Elasticsearch.node.js", "dist/nodes/Elastic/ElasticSecurity/ElasticSecurity.node.js", "dist/nodes/EmailReadImap/EmailReadImap.node.js", "dist/nodes/EmailSend/EmailSend.node.js", "dist/nodes/Emelia/Emelia.node.js", "dist/nodes/<PERSON>elia/Emelia<PERSON>rigger.node.js", "dist/nodes/ERPNext/ERPNext.node.js", "dist/nodes/ErrorTrigger/ErrorTrigger.node.js", "dist/nodes/Evaluation/EvaluationTrigger/EvaluationTrigger.node.ee.js", "dist/nodes/Evaluation/Evaluation/Evaluation.node.ee.js", "dist/nodes/Eventbrite/EventbriteTrigger.node.js", "dist/nodes/ExecuteCommand/ExecuteCommand.node.js", "dist/nodes/ExecuteWorkflow/ExecuteWorkflow/ExecuteWorkflow.node.js", "dist/nodes/ExecuteWorkflow/ExecuteWorkflowTrigger/ExecuteWorkflowTrigger.node.js", "dist/nodes/ExecutionData/ExecutionData.node.js", "dist/nodes/Facebook/FacebookGraphApi.node.js", "dist/nodes/Facebook/FacebookTrigger.node.js", "dist/nodes/FacebookLeadAds/FacebookLeadAdsTrigger.node.js", "dist/nodes/Figma/FigmaTrigger.node.js", "dist/nodes/FileMaker/FileMaker.node.js", "dist/nodes/Files/ReadWriteFile/ReadWriteFile.node.js", "dist/nodes/Files/ConvertToFile/ConvertToFile.node.js", "dist/nodes/Files/ExtractFromFile/ExtractFromFile.node.js", "dist/nodes/Filter/Filter.node.js", "dist/nodes/Flow/Flow.node.js", "dist/nodes/Flow/FlowTrigger.node.js", "dist/nodes/Form/Form.node.js", "dist/nodes/Form/FormTrigger.node.js", "dist/nodes/FormIo/FormIoTrigger.node.js", "dist/nodes/Formstack/FormstackTrigger.node.js", "dist/nodes/Freshdesk/Freshdesk.node.js", "dist/nodes/Freshservice/Freshservice.node.js", "dist/nodes/FreshworksCrm/FreshworksCrm.node.js", "dist/nodes/Ftp/Ftp.node.js", "dist/nodes/Function/Function.node.js", "dist/nodes/FunctionItem/FunctionItem.node.js", "dist/nodes/GetResponse/GetResponse.node.js", "dist/nodes/GetResponse/GetResponseTrigger.node.js", "dist/nodes/Ghost/Ghost.node.js", "dist/nodes/Git/Git.node.js", "dist/nodes/Github/Github.node.js", "dist/nodes/Github/GithubTrigger.node.js", "dist/nodes/Gitlab/Gitlab.node.js", "dist/nodes/Gitlab/GitlabTrigger.node.js", "dist/nodes/Gong/Gong.node.js", "dist/nodes/Google/Ads/GoogleAds.node.js", "dist/nodes/Google/Analytics/GoogleAnalytics.node.js", "dist/nodes/Google/BigQuery/GoogleBigQuery.node.js", "dist/nodes/Google/Books/GoogleBooks.node.js", "dist/nodes/Google/Calendar/GoogleCalendar.node.js", "dist/nodes/Google/Calendar/GoogleCalendarTrigger.node.js", "dist/nodes/Google/Chat/GoogleChat.node.js", "dist/nodes/Google/CloudNaturalLanguage/GoogleCloudNaturalLanguage.node.js", "dist/nodes/Google/CloudStorage/GoogleCloudStorage.node.js", "dist/nodes/Google/Contacts/GoogleContacts.node.js", "dist/nodes/Google/Docs/GoogleDocs.node.js", "dist/nodes/Google/Drive/GoogleDrive.node.js", "dist/nodes/Google/Drive/GoogleDriveTrigger.node.js", "dist/nodes/Google/Firebase/CloudFirestore/GoogleFirebaseCloudFirestore.node.js", "dist/nodes/Google/Firebase/RealtimeDatabase/GoogleFirebaseRealtimeDatabase.node.js", "dist/nodes/Google/Gmail/Gmail.node.js", "dist/nodes/Google/Gmail/GmailTrigger.node.js", "dist/nodes/Google/GSuiteAdmin/GSuiteAdmin.node.js", "dist/nodes/Google/BusinessProfile/GoogleBusinessProfile.node.js", "dist/nodes/Google/BusinessProfile/GoogleBusinessProfileTrigger.node.js", "dist/nodes/Google/Perspective/GooglePerspective.node.js", "dist/nodes/Google/Sheet/GoogleSheets.node.js", "dist/nodes/Google/Sheet/GoogleSheetsTrigger.node.js", "dist/nodes/Google/Slides/GoogleSlides.node.js", "dist/nodes/Google/Task/GoogleTasks.node.js", "dist/nodes/Google/Translate/GoogleTranslate.node.js", "dist/nodes/Google/YouTube/YouTube.node.js", "dist/nodes/Gotify/Gotify.node.js", "dist/nodes/GoToWebinar/GoToWebinar.node.js", "dist/nodes/Grafana/Grafana.node.js", "dist/nodes/GraphQL/GraphQL.node.js", "dist/nodes/Grist/Grist.node.js", "dist/nodes/Gumroad/GumroadTrigger.node.js", "dist/nodes/HackerNews/HackerNews.node.js", "dist/nodes/HaloPSA/HaloPSA.node.js", "dist/nodes/Harvest/Harvest.node.js", "dist/nodes/HelpScout/HelpScout.node.js", "dist/nodes/HelpScout/HelpScoutTrigger.node.js", "dist/nodes/HighLevel/HighLevel.node.js", "dist/nodes/HomeAssistant/HomeAssistant.node.js", "dist/nodes/HtmlExtract/HtmlExtract.node.js", "dist/nodes/Html/Html.node.js", "dist/nodes/HttpRequest/HttpRequest.node.js", "dist/nodes/Hubspot/Hubspot.node.js", "dist/nodes/Hubspot/HubspotTrigger.node.js", "dist/nodes/HumanticAI/HumanticAi.node.js", "dist/nodes/Hunter/Hunter.node.js", "dist/nodes/ICalendar/ICalendar.node.js", "dist/nodes/If/If.node.js", "dist/nodes/Intercom/Intercom.node.js", "dist/nodes/Interval/Interval.node.js", "dist/nodes/InvoiceNinja/InvoiceNinja.node.js", "dist/nodes/InvoiceNinja/InvoiceNinjaTrigger.node.js", "dist/nodes/ItemLists/ItemLists.node.js", "dist/nodes/Iterable/Iterable.node.js", "dist/nodes/Jenkins/Jenkins.node.js", "dist/nodes/JinaAI/JinaAi.node.js", "dist/nodes/Jira/Jira.node.js", "dist/nodes/Jira/JiraTrigger.node.js", "dist/nodes/JotForm/JotFormTrigger.node.js", "dist/nodes/Jwt/Jwt.node.js", "dist/nodes/Kafka/Kafka.node.js", "dist/nodes/Kafka/KafkaTrigger.node.js", "dist/nodes/Keap/Keap.node.js", "dist/nodes/Keap/KeapTrigger.node.js", "dist/nodes/Kitemaker/Kitemaker.node.js", "dist/nodes/KoBoToolbox/KoBoToolbox.node.js", "dist/nodes/KoBoToolbox/KoBoToolboxTrigger.node.js", "dist/nodes/Ldap/Ldap.node.js", "dist/nodes/Lemlist/Lemlist.node.js", "dist/nodes/Lemlist/LemlistTrigger.node.js", "dist/nodes/Line/Line.node.js", "dist/nodes/Linear/Linear.node.js", "dist/nodes/Linear/LinearTrigger.node.js", "dist/nodes/LingvaNex/LingvaNex.node.js", "dist/nodes/LinkedIn/LinkedIn.node.js", "dist/nodes/LocalFileTrigger/LocalFileTrigger.node.js", "dist/nodes/LoneScale/LoneScaleTrigger.node.js", "dist/nodes/LoneScale/LoneScale.node.js", "dist/nodes/Magento/Magento2.node.js", "dist/nodes/Mailcheck/Mailcheck.node.js", "dist/nodes/Mailchimp/Mailchimp.node.js", "dist/nodes/Mailchimp/MailchimpTrigger.node.js", "dist/nodes/MailerLite/MailerLite.node.js", "dist/nodes/MailerLite/MailerLiteTrigger.node.js", "dist/nodes/Mailgun/Mailgun.node.js", "dist/nodes/Mailjet/Mailjet.node.js", "dist/nodes/Mailjet/MailjetTrigger.node.js", "dist/nodes/Mandrill/Mandrill.node.js", "dist/nodes/ManualTrigger/ManualTrigger.node.js", "dist/nodes/Markdown/Markdown.node.js", "dist/nodes/Marketstack/Marketstack.node.js", "dist/nodes/Matrix/Matrix.node.js", "dist/nodes/Mattermost/Mattermost.node.js", "dist/nodes/Mautic/Mautic.node.js", "dist/nodes/Mautic/MauticTrigger.node.js", "dist/nodes/Medium/Medium.node.js", "dist/nodes/Merge/Merge.node.js", "dist/nodes/MessageBird/MessageBird.node.js", "dist/nodes/Metabase/Metabase.node.js", "dist/nodes/Microsoft/AzureCosmosDb/AzureCosmosDb.node.js", "dist/nodes/Microsoft/Dynamics/MicrosoftDynamicsCrm.node.js", "dist/nodes/Microsoft/Entra/MicrosoftEntra.node.js", "dist/nodes/Microsoft/Excel/MicrosoftExcel.node.js", "dist/nodes/Microsoft/GraphSecurity/MicrosoftGraphSecurity.node.js", "dist/nodes/Microsoft/OneDrive/MicrosoftOneDrive.node.js", "dist/nodes/Microsoft/OneDrive/MicrosoftOneDriveTrigger.node.js", "dist/nodes/Microsoft/Outlook/MicrosoftOutlook.node.js", "dist/nodes/Microsoft/Outlook/MicrosoftOutlookTrigger.node.js", "dist/nodes/Microsoft/SharePoint/MicrosoftSharePoint.node.js", "dist/nodes/Microsoft/Sql/MicrosoftSql.node.js", "dist/nodes/Microsoft/Storage/AzureStorage.node.js", "dist/nodes/Microsoft/Teams/MicrosoftTeams.node.js", "dist/nodes/Microsoft/Teams/MicrosoftTeamsTrigger.node.js", "dist/nodes/Microsoft/ToDo/MicrosoftToDo.node.js", "dist/nodes/Mindee/Mindee.node.js", "dist/nodes/Misp/Misp.node.js", "dist/nodes/MistralAI/MistralAi.node.js", "dist/nodes/Mocean/Mocean.node.js", "dist/nodes/MondayCom/MondayCom.node.js", "dist/nodes/MongoDb/MongoDb.node.js", "dist/nodes/MonicaCrm/MonicaCrm.node.js", "dist/nodes/MoveBinaryData/MoveBinaryData.node.js", "dist/nodes/MQTT/Mqtt.node.js", "dist/nodes/MQTT/MqttTrigger.node.js", "dist/nodes/Msg91/Msg91.node.js", "dist/nodes/MySql/MySql.node.js", "dist/nodes/N8n/N8n.node.js", "dist/nodes/N8nTrainingCustomerDatastore/N8nTrainingCustomerDatastore.node.js", "dist/nodes/N8nTrainingCustomerMessenger/N8nTrainingCustomerMessenger.node.js", "dist/nodes/N8nTrigger/N8nTrigger.node.js", "dist/nodes/Nasa/Nasa.node.js", "dist/nodes/Netlify/Netlify.node.js", "dist/nodes/Netlify/NetlifyTrigger.node.js", "dist/nodes/NextCloud/NextCloud.node.js", "dist/nodes/NocoDB/NocoDB.node.js", "dist/nodes/Brevo/Brevo.node.js", "dist/nodes/Brevo/BrevoTrigger.node.js", "dist/nodes/StickyNote/StickyNote.node.js", "dist/nodes/NoOp/NoOp.node.js", "dist/nodes/Onfleet/Onfleet.node.js", "dist/nodes/Onfleet/OnfleetTrigger.node.js", "dist/nodes/Netscaler/ADC/NetscalerAdc.node.js", "dist/nodes/Notion/Notion.node.js", "dist/nodes/Notion/NotionTrigger.node.js", "dist/nodes/Npm/Npm.node.js", "dist/nodes/Odoo/Odoo.node.js", "dist/nodes/Okta/Okta.node.js", "dist/nodes/OneSimpleApi/OneSimpleApi.node.js", "dist/nodes/OpenAi/OpenAi.node.js", "dist/nodes/OpenThesaurus/OpenThesaurus.node.js", "dist/nodes/OpenWeatherMap/OpenWeatherMap.node.js", "dist/nodes/Orbit/Orbit.node.js", "dist/nodes/Oura/Oura.node.js", "dist/nodes/Paddle/Paddle.node.js", "dist/nodes/PagerDuty/PagerDuty.node.js", "dist/nodes/PayPal/PayPal.node.js", "dist/nodes/PayPal/PayPalTrigger.node.js", "dist/nodes/Peekalink/Peekalink.node.js", "dist/nodes/Perplexity/Perplexity.node.js", "dist/nodes/Phantombuster/Phantombuster.node.js", "dist/nodes/PhilipsHue/PhilipsHue.node.js", "dist/nodes/Pipedrive/Pipedrive.node.js", "dist/nodes/Pipedrive/PipedriveTrigger.node.js", "dist/nodes/Plivo/Plivo.node.js", "dist/nodes/PostBin/PostBin.node.js", "dist/nodes/Postgres/Postgres.node.js", "dist/nodes/Postgres/PostgresTrigger.node.js", "dist/nodes/PostHog/PostHog.node.js", "dist/nodes/Postmark/PostmarkTrigger.node.js", "dist/nodes/ProfitWell/ProfitWell.node.js", "dist/nodes/Pushbullet/Pushbullet.node.js", "dist/nodes/Pushcut/Pushcut.node.js", "dist/nodes/Pushcut/PushcutTrigger.node.js", "dist/nodes/Pushover/Pushover.node.js", "dist/nodes/QuestDb/QuestDb.node.js", "dist/nodes/QuickBase/QuickBase.node.js", "dist/nodes/QuickBooks/QuickBooks.node.js", "dist/nodes/QuickChart/QuickChart.node.js", "dist/nodes/RabbitMQ/RabbitMQ.node.js", "dist/nodes/RabbitMQ/RabbitMQTrigger.node.js", "dist/nodes/Raindrop/Raindrop.node.js", "dist/nodes/ReadBinaryFile/ReadBinaryFile.node.js", "dist/nodes/ReadBinaryFiles/ReadBinaryFiles.node.js", "dist/nodes/ReadPdf/ReadPDF.node.js", "dist/nodes/Reddit/Reddit.node.js", "dist/nodes/Redis/Redis.node.js", "dist/nodes/Redis/RedisTrigger.node.js", "dist/nodes/RenameKeys/RenameKeys.node.js", "dist/nodes/RespondToWebhook/RespondToWebhook.node.js", "dist/nodes/Rocketchat/Rocketchat.node.js", "dist/nodes/RssFeedRead/RssFeedRead.node.js", "dist/nodes/RssFeedRead/RssFeedReadTrigger.node.js", "dist/nodes/Rundeck/Rundeck.node.js", "dist/nodes/S3/S3.node.js", "dist/nodes/Salesforce/Salesforce.node.js", "dist/nodes/Salesforce/SalesforceTrigger.node.js", "dist/nodes/Salesmate/Salesmate.node.js", "dist/nodes/Schedule/ScheduleTrigger.node.js", "dist/nodes/SeaTable/SeaTable.node.js", "dist/nodes/SeaTable/SeaTableTrigger.node.js", "dist/nodes/SecurityScorecard/SecurityScorecard.node.js", "dist/nodes/Segment/Segment.node.js", "dist/nodes/SendGrid/SendGrid.node.js", "dist/nodes/Sendy/Sendy.node.js", "dist/nodes/SentryIo/SentryIo.node.js", "dist/nodes/ServiceNow/ServiceNow.node.js", "dist/nodes/Set/Set.node.js", "dist/nodes/Shopify/Shopify.node.js", "dist/nodes/Shopify/ShopifyTrigger.node.js", "dist/nodes/Signl4/Signl4.node.js", "dist/nodes/Simulate/Simulate.node.js", "dist/nodes/Simulate/SimulateTrigger.node.js", "dist/nodes/Slack/Slack.node.js", "dist/nodes/Slack/SlackTrigger.node.js", "dist/nodes/Sms77/Sms77.node.js", "dist/nodes/Snowflake/Snowflake.node.js", "dist/nodes/SplitInBatches/SplitInBatches.node.js", "dist/nodes/Splunk/Splunk.node.js", "dist/nodes/Spontit/Spontit.node.js", "dist/nodes/Spotify/Spotify.node.js", "dist/nodes/SpreadsheetFile/SpreadsheetFile.node.js", "dist/nodes/SseTrigger/SseTrigger.node.js", "dist/nodes/Ssh/Ssh.node.js", "dist/nodes/Stackby/Stackby.node.js", "dist/nodes/Start/Start.node.js", "dist/nodes/StopAndError/StopAndError.node.js", "dist/nodes/Storyblok/Storyblok.node.js", "dist/nodes/Strapi/Strapi.node.js", "dist/nodes/Strava/Strava.node.js", "dist/nodes/Strava/StravaTrigger.node.js", "dist/nodes/Stripe/Stripe.node.js", "dist/nodes/Stripe/StripeTrigger.node.js", "dist/nodes/Supabase/Supabase.node.js", "dist/nodes/SurveyMonkey/SurveyMonkeyTrigger.node.js", "dist/nodes/Switch/Switch.node.js", "dist/nodes/SyncroMSP/SyncroMsp.node.js", "dist/nodes/Taiga/Taiga.node.js", "dist/nodes/Taiga/TaigaTrigger.node.js", "dist/nodes/Tapfiliate/Tapfiliate.node.js", "dist/nodes/Telegram/Telegram.node.js", "dist/nodes/Telegram/TelegramTrigger.node.js", "dist/nodes/TheHiveProject/TheHiveProject.node.js", "dist/nodes/TheHiveProject/TheHiveProjectTrigger.node.js", "dist/nodes/TheHive/TheHive.node.js", "dist/nodes/TheHive/TheHiveTrigger.node.js", "dist/nodes/TimescaleDb/TimescaleDb.node.js", "dist/nodes/Todoist/Todoist.node.js", "dist/nodes/Toggl/TogglTrigger.node.js", "dist/nodes/Totp/Totp.node.js", "dist/nodes/TravisCi/TravisCi.node.js", "dist/nodes/Trello/Trello.node.js", "dist/nodes/Trello/TrelloTrigger.node.js", "dist/nodes/Twake/Twake.node.js", "dist/nodes/Twilio/Twilio.node.js", "dist/nodes/Twilio/TwilioTrigger.node.js", "dist/nodes/Twist/Twist.node.js", "dist/nodes/Twitter/Twitter.node.js", "dist/nodes/Typeform/TypeformTrigger.node.js", "dist/nodes/UnleashedSoftware/UnleashedSoftware.node.js", "dist/nodes/Uplead/Uplead.node.js", "dist/nodes/UProc/UProc.node.js", "dist/nodes/UptimeRobot/UptimeRobot.node.js", "dist/nodes/UrlScanIo/UrlScanIo.node.js", "dist/nodes/Vero/Vero.node.js", "dist/nodes/Venafi/ProtectCloud/VenafiTlsProtectCloud.node.js", "dist/nodes/Venafi/ProtectCloud/VenafiTlsProtectCloudTrigger.node.js", "dist/nodes/Venafi/Datacenter/VenafiTlsProtectDatacenter.node.js", "dist/nodes/Vonage/Vonage.node.js", "dist/nodes/Wait/Wait.node.js", "dist/nodes/Webflow/Webflow.node.js", "dist/nodes/Webflow/WebflowTrigger.node.js", "dist/nodes/Webhook/Webhook.node.js", "dist/nodes/Wekan/Wekan.node.js", "dist/nodes/WhatsApp/WhatsAppTrigger.node.js", "dist/nodes/WhatsApp/WhatsApp.node.js", "dist/nodes/Wise/Wise.node.js", "dist/nodes/Wise/WiseTrigger.node.js", "dist/nodes/WooCommerce/WooCommerce.node.js", "dist/nodes/WooCommerce/WooCommerceTrigger.node.js", "dist/nodes/Wordpress/Wordpress.node.js", "dist/nodes/Workable/WorkableTrigger.node.js", "dist/nodes/WorkflowTrigger/WorkflowTrigger.node.js", "dist/nodes/WriteBinaryFile/WriteBinaryFile.node.js", "dist/nodes/Wufoo/WufooTrigger.node.js", "dist/nodes/Xero/Xero.node.js", "dist/nodes/Xml/Xml.node.js", "dist/nodes/Yourls/Yourls.node.js", "dist/nodes/Zammad/Zammad.node.js", "dist/nodes/Zendesk/Zendesk.node.js", "dist/nodes/Zendesk/ZendeskTrigger.node.js", "dist/nodes/Zoho/ZohoCrm.node.js", "dist/nodes/Zoom/Zoom.node.js", "dist/nodes/Zulip/Zulip.node.js", "dist/nodes/Transform/Aggregate/Aggregate.node.js", "dist/nodes/Transform/Limit/Limit.node.js", "dist/nodes/Transform/RemoveDuplicates/RemoveDuplicates.node.js", "dist/nodes/Transform/SplitOut/SplitOut.node.js", "dist/nodes/Transform/Sort/Sort.node.js", "dist/nodes/Transform/Summarize/Summarize.node.js"]}, "devDependencies": {"@n8n/typescript-config": "workspace:*", "@types/amqplib": "^0.10.1", "@types/aws4": "^1.5.1", "@types/basic-auth": "catalog:", "@types/cheerio": "^0.22.15", "@types/eventsource": "^1.1.2", "@types/express": "catalog:", "@types/gm": "^1.25.0", "@types/html-to-text": "^9.0.1", "@types/js-nacl": "^1.3.0", "@types/jsonwebtoken": "catalog:", "@types/lodash": "catalog:", "@types/lossless-json": "^1.0.0", "@types/mailparser": "^3.4.4", "@types/mime-types": "^2.1.0", "@types/mssql": "^9.1.5", "@types/nodemailer": "^6.4.14", "@types/promise-ftp": "^1.3.4", "@types/rfc2047": "^2.0.1", "@types/sanitize-html": "^2.11.0", "@types/showdown": "^1.9.4", "@types/ssh2-sftp-client": "^5.1.0", "@types/uuid": "catalog:", "@types/xml2js": "catalog:", "eslint-plugin-n8n-nodes-base": "^1.16.3", "n8n-core": "workspace:*"}, "dependencies": {"@aws-sdk/client-sso-oidc": "3.808.0", "@kafkajs/confluent-schema-registry": "3.8.0", "@mozilla/readability": "0.6.0", "@n8n/config": "workspace:*", "@n8n/di": "workspace:*", "@n8n/imap": "workspace:*", "@n8n/vm2": "3.9.25", "alasql": "4.4.0", "amqplib": "0.10.6", "aws4": "1.11.0", "basic-auth": "catalog:", "change-case": "4.1.2", "cheerio": "1.0.0-rc.6", "chokidar": "catalog:", "cron": "3.1.7", "csv-parse": "5.5.0", "currency-codes": "2.1.0", "eventsource": "2.0.2", "fast-glob": "catalog:", "fastest-levenshtein": "^1.0.16", "fflate": "0.7.4", "generate-schema": "2.6.0", "get-system-fonts": "2.0.2", "gm": "1.25.1", "html-to-text": "9.0.5", "iconv-lite": "catalog:", "ics": "2.40.0", "isbot": "3.6.13", "iso-639-1": "2.1.15", "js-nacl": "1.4.0", "jsdom": "23.0.1", "jsonwebtoken": "catalog:", "kafkajs": "2.2.4", "ldapts": "4.2.6", "lodash": "catalog:", "lossless-json": "1.0.5", "luxon": "catalog:", "mailparser": "3.6.7", "minifaker": "1.34.1", "moment-timezone": "0.5.37", "mongodb": "6.11.0", "mqtt": "5.7.2", "mssql": "10.0.2", "mysql2": "3.11.0", "n8n-workflow": "workspace:*", "node-html-markdown": "1.2.0", "node-ssh": "13.2.0", "nodemailer": "6.9.9", "otpauth": "9.1.1", "pdfjs-dist": "5.3.31", "pg": "8.12.0", "pg-promise": "11.9.1", "promise-ftp": "1.3.5", "pyodide": "0.27.5", "redis": "4.6.14", "rfc2047": "4.0.1", "rhea": "1.0.24", "rrule": "2.8.1", "rss-parser": "3.13.0", "sanitize-html": "2.12.1", "semver": "7.5.4", "showdown": "2.1.0", "simple-git": "3.17.0", "snowflake-sdk": "2.1.0", "ssh2-sftp-client": "7.2.3", "tmp-promise": "3.0.3", "ts-ics": "1.2.2", "uuid": "catalog:", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "xml2js": "catalog:", "xmlhttprequest-ssl": "3.1.0"}}