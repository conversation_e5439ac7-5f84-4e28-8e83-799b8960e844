#!/usr/bin/env node

/**
 * Test script to verify GET /login auto-login functionality
 * This script tests the modified GET /login endpoint that now includes auto-login for galaxy user
 */

const http = require('http');

async function testGetLogin() {
    console.log('Testing GET /login auto-login functionality...');
    
    const options = {
        hostname: 'localhost',
        port: 5678,
        path: '/rest/login',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'browser-id': 'test-browser-id'
        }
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            console.log(`Status Code: ${res.statusCode}`);
            console.log('Response Headers:', res.headers);
            
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('Login response received');
                    console.log('User info:', {
                        id: response.id,
                        email: response.email,
                        firstName: response.firstName,
                        lastName: response.lastName,
                        role: response.role,
                        isOwner: response.isOwner
                    });
                    
                    // Check if we got a Set-Cookie header with n8n-auth
                    const setCookieHeader = res.headers['set-cookie'];
                    if (setCookieHeader && setCookieHeader.some(cookie => cookie.includes('n8n-auth'))) {
                        console.log('✅ Auto-login successful! Authentication cookie set.');
                        console.log('Cookie:', setCookieHeader.find(cookie => cookie.includes('n8n-auth')));
                        
                        // Verify user details
                        if (response.email === '<EMAIL>') {
                            console.log('✅ Correct galaxy user logged in.');
                        } else {
                            console.log('❌ Wrong user logged in. Expected <EMAIL>, got:', response.email);
                        }
                        
                        if (response.isOwner) {
                            console.log('✅ User has owner privileges.');
                        } else {
                            console.log('❌ User does not have owner privileges.');
                        }
                    } else {
                        console.log('❌ Auto-login failed. No authentication cookie found.');
                    }
                    
                    resolve(response);
                } catch (error) {
                    console.error('Error parsing response:', error);
                    console.log('Raw response:', data);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('Request error:', error);
            reject(error);
        });

        req.end();
    });
}

// Test with existing cookie (should return current user info)
async function testWithExistingCookie() {
    console.log('\nTesting GET /login with existing cookie...');
    
    const options = {
        hostname: 'localhost',
        port: 5678,
        path: '/rest/login',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'browser-id': 'test-browser-id',
            'Cookie': 'n8n-auth=existing-token'
        }
    };

    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            console.log(`Status Code: ${res.statusCode}`);
            
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('Response received for existing cookie test');
                    
                    // Should not set new cookie if user is already authenticated
                    const setCookieHeader = res.headers['set-cookie'];
                    if (!setCookieHeader || !setCookieHeader.some(cookie => cookie.includes('n8n-auth'))) {
                        console.log('✅ Correctly handled existing authentication.');
                    } else {
                        console.log('ℹ️  New cookie set (this might be normal for token refresh).');
                    }
                    
                    resolve(response);
                } catch (error) {
                    // This might fail with invalid token, which is expected
                    console.log('ℹ️  Request failed with existing invalid token (expected behavior)');
                    resolve();
                }
            });
        });

        req.on('error', (error) => {
            console.error('Request error:', error);
            reject(error);
        });

        req.end();
    });
}

async function main() {
    try {
        console.log('='.repeat(60));
        console.log('Testing GET /login Auto-Login Functionality');
        console.log('='.repeat(60));
        
        await testGetLogin();
        await testWithExistingCookie();
        
        console.log('\n' + '='.repeat(60));
        console.log('Test completed.');
        console.log('='.repeat(60));
    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
