packages:
  - packages/*
  - packages/@n8n/*
  - packages/frontend/**
  - packages/extensions/**
  - cypress
  - packages/testing/**

catalog:
  '@n8n/typeorm': 0.3.20-12
  '@n8n_io/ai-assistant-sdk': 1.15.0
  '@langchain/core': 0.3.61
  '@langchain/openai': 0.5.16
  '@langchain/anthropic': 0.3.23
  '@langchain/community': 0.3.47
  '@sentry/node': 8.52.1
  '@types/basic-auth': ^1.1.3
  '@types/express': ^5.0.1
  '@types/jsonwebtoken': ^9.0.9
  '@types/lodash': 4.17.17
  '@types/uuid': ^10.0.0
  '@types/xml2js': ^0.4.14
  '@vitest/coverage-v8': 3.2.4
  axios: 1.8.3
  basic-auth: 2.0.1
  callsites: 3.1.0
  chokidar: 4.0.1
  fast-glob: 3.2.12
  flatted: 3.2.7
  form-data: 4.0.0
  http-proxy-agent: 7.0.2
  https-proxy-agent: 7.0.6
  iconv-lite: 0.6.3
  jsonwebtoken: 9.0.2
  js-base64: 3.7.2
  lodash: 4.17.21
  luxon: 3.4.4
  nanoid: 3.3.8
  picocolors: 1.0.1
  reflect-metadata: 0.2.2
  rimraf: ^6.0.1
  tsup: ^8.5.0
  tsx: ^4.19.3
  uuid: 10.0.0
  vite: ^6.3.5
  vite-plugin-dts: ^4.5.4
  vitest: ^3.1.3
  vitest-mock-extended: ^3.1.0
  xml2js: 0.6.2
  xss: 1.0.15
  zod: 3.25.67
  zod-to-json-schema: 3.23.3
  typescript: 5.8.3
  eslint: 9.29.0

catalogs:
  frontend:
    '@sentry/vue': ^8.33.1
    '@testing-library/jest-dom': ^6.6.3
    '@testing-library/user-event': ^14.6.1
    '@testing-library/vue': ^8.1.0
    '@vue/tsconfig': ^0.7.0
    '@vueuse/core': ^10.11.0
    '@vitejs/plugin-vue': ^5.2.4
    pinia: ^2.2.4
    unplugin-icons: ^0.19.0
    unplugin-vue-components: ^0.27.2
    vue: ^3.5.13
    vue-i18n: ^11.1.2
    vue-router: ^4.5.0
    vue-tsc: ^2.2.8
    vue-markdown-render: ^2.2.1
    highlight.js: ^11.8.0
    element-plus: 2.4.3
